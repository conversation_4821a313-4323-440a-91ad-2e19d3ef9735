# D&D Campaign Assistant - Product Design Requirements (PDR)

## Overview

This PDR defines the systematic development of a real-time D&D campaign assistant that processes live audio transcription to enhance tabletop gameplay. The system extends the existing `localtail` infrastructure with an agent-based architecture for intelligent content processing and real-time event generation.

## Core Project Assumptions

### Assumption 1: Real-time Audio Transcription File
There exists a local real-time audio transcription file that contains TTRPG content. This file is continuously updated with dialogue, DM narration, combat descriptions, and other table activity. Lorecat does not implement speech-to-text functionality itself, as this space is rapidly evolving and often requires paid services (AssemblyAI, Deepgram, etc.).

### Assumption 2: Local-Only Operation
Lorecat will always run locally and never be hosted on the internet. This ensures:
- Complete privacy of campaign data
- No dependency on external services during gameplay
- Zero latency for real-time processing
- Full control over data storage and access

### Assumption 3: Campaign Continuity
Upon startup, lorecat has access to:
- The current contents of the transcription file
- Previously stored campaign lore data from past sessions
- A top-level concept of a "campaign" linking multiple adventure sessions

## Current Progress

**Status**: Phase 2 In Progress - Agent System Foundation 🚧

### Completed: Phase 1 - WebSocket Event Broadcasting ✅
**Completed**: December 19, 2024

### Completed: Campaign Data Storage Foundation ✅
**Completed**: August 19, 2025

**Deliverables Completed**:
- ✅ `CampaignDataManager` class with full CRUD operations
- ✅ JSON file-based storage with directory structure
- ✅ In-memory caching and smart indexing system
- ✅ Fuzzy search for transcription error tolerance
- ✅ Real-time entity mention tracking and timeline
- ✅ Session event logging with timestamps
- ✅ Backup and recovery system
- ✅ Comprehensive test suite (31 tests, 100% pass rate)
- ✅ Performance benchmarks (<10ms for common operations)

**Success Criteria Met**:
- ✅ Human-readable JSON format for manual editing
- ✅ Fast in-memory lookups for real-time performance
- ✅ Atomic file operations prevent data corruption
- ✅ Campaign continuity across sessions
- ✅ Extensible schema for future entity types

**Achievements**:
- ✅ **WebSocket Server Integration**: Integrated WebSocket server with existing Express HTTP server using the `ws` library
- ✅ **Real-time Event Broadcasting**: Replaced 500ms polling with instant WebSocket-based file update notifications
- ✅ **Frontend WebSocket Client**: Implemented WebSocket client with automatic reconnection and error handling
- ✅ **Connection Management**: Robust WebSocket lifecycle management including reconnection logic with exponential backoff
- ✅ **Structured Message Protocol**: Defined event types (`connection`, `fileUpdate`, `error`) with proper message structure
- ✅ **Performance Optimization**: Removed `totalContent` from WebSocket messages to prevent performance issues with large files (1-2MB+)
- ✅ **Comprehensive Testing**: 11 integration tests covering WebSocket functionality, performance, and reliability

**Performance Metrics Achieved**:
- **Latency**: Sub-50ms from file change to client notification (down from 500ms polling)
- **Scalability**: Supports multiple concurrent WebSocket connections without performance degradation
- **Reliability**: Handles client disconnections, rapid file updates, and long-duration sessions
- **Memory Efficiency**: WebSocket messages now ~1KB instead of potentially 2MB+ for large files

**Technical Foundation Established**:
- Real-time event distribution infrastructure ready for agent system integration
- WebSocket-based communication channel for future agent state updates and notifications
- Performance-optimized message protocol suitable for high-frequency agent events
- Robust error handling and connection management for production use

### Next: Phase 2 - Agent System Foundation
**Objective**: Establish core agent architecture building on the WebSocket infrastructure

**Planned Deliverables**:
- `TailAgentSystem` class extending existing localtail functionality
- `BaseAgent` abstract class defining standardized agent interface
- Agent registration and lifecycle management system
- Global state management for cross-agent communication
- Content processing pipeline with parallel agent execution

## Goals

### Primary Goals
1. **Real-time Campaign Enhancement** - Process live audio transcription to provide immediate campaign assistance
2. **Modular Agent Architecture** - Enable independent development and deployment of specialized D&D features
3. **Non-intrusive Operation** - Enhance gameplay without disrupting the natural flow of the session
4. **Extensible Intelligence** - Support future integration with LLMs and external APIs for advanced features

### Success Metrics
- Sub-500ms latency from transcript update to agent processing
- Zero session interruptions due to system failures
- Successful detection of 90%+ location changes and NPC mentions
- Stable operation during 4+ hour gaming sessions

## Technical Requirements

### Implementation Approach

#### Phase 1: Agent System Foundation
**Objective**: Establish core agent architecture without D&D-specific features

**Status**: 🔄 Next Priority - Ready to Begin

**Deliverables**:
1. `TailAgentSystem` class extending existing `localtail` functionality
2. `BaseAgent` abstract class defining agent interface
3. Agent registration and lifecycle management
4. Basic event emission and global state management
5. Integration with `CampaignDataManager` for persistent state
6. Implementation of standardized Agent API Conventions
7. Comprehensive test suite for agent system core

**API Specification**: See `Agent-API-Conventions.md` for detailed implementation patterns

**Success Criteria**:
- Agent system can register/unregister agents dynamically
- Global state updates propagate correctly to all agents
- System handles agent failures gracefully without affecting other agents
- Performance benchmarks show <100ms overhead per agent per processing cycle
- Seamless integration with campaign data storage

#### Phase 2: WebSocket Event Broadcasting
**Objective**: Enable real-time communication between server and frontend

**Deliverables**:
1. WebSocket server integration with existing HTTP server
2. Event broadcasting system for agent updates
3. Frontend WebSocket client integration
4. Connection management and reconnection logic
5. Event message protocol definition

**Success Criteria**:
- WebSocket connections remain stable during long sessions
- All agent events reach connected clients within 50ms
- System handles client disconnections/reconnections gracefully
- Frontend displays real-time agent status updates

#### Phase 3: Location Tracking Agent
**Objective**: Implement first D&D-specific agent for location awareness

**Deliverables**:
1. `LocationTracker` agent with keyword-based detection
2. Location extraction algorithms (regex-based initially)
3. Location history tracking and persistence
4. Frontend location display component
5. Location change notifications

**Success Criteria**:
- Detects 90%+ of explicit location mentions ("enters the tavern", "arrives at the castle")
- Maintains accurate location history throughout session
- False positive rate <10% for location detection
- Location changes appear in UI within 1 second of transcript update

#### Phase 4: NPC Detection and Management
**Objective**: Track character mentions and interactions

**Deliverables**:
1. `NPCTracker` agent for character name detection
2. NPC database integration using JSON file storage
3. Character interaction logging with timeline tracking
4. NPC status and relationship tracking
5. Frontend NPC panel with active character list
6. `CampaignDataManager` integration for persistent NPC data

**Success Criteria**:
- Identifies known NPCs from campaign database with 95%+ accuracy
- Tracks NPC interaction frequency and recency
- Suggests relevant NPC information when characters are mentioned
- Maintains NPC state across multiple sessions
- Persists NPC data in human-readable JSON format
- Supports fuzzy matching for transcription errors

#### Phase 5: Combat and Dice Roll Detection
**Objective**: Enhance combat encounters with automated tracking

**Deliverables**:
1. `CombatTracker` agent for initiative and turn management
2. `DiceRollParser` agent for roll result extraction
3. Combat state management (initiative order, HP tracking)
4. Dice roll history and statistics
5. Combat-specific UI components

**Success Criteria**:
- Detects combat start/end with 90%+ accuracy
- Parses dice roll results from natural language ("rolled a 15")
- Maintains initiative order and turn tracking
- Provides combat summary and statistics

#### Phase 6: LLM Integration Framework
**Objective**: Enable AI-powered content analysis and generation

**Deliverables**:
1. `LLMAgent` base class for external API integration
2. Rate limiting and API key management
3. Context window management for large transcripts
4. `StoryAnalyzer` agent for narrative beat detection
5. `EncounterGenerator` agent for dynamic content creation

**Success Criteria**:
- LLM agents operate within API rate limits
- Context summarization maintains story coherence
- Generated content aligns with campaign tone and setting
- System degrades gracefully when LLM services are unavailable

### Agent System Architecture

#### Core Components

**TailAgentSystem**:
- Manages agent lifecycle and execution scheduling
- Provides global state management across agents
- Handles content chunking and distribution to agents
- Implements error isolation between agents

**BaseAgent Interface**:
- Standardized agent lifecycle (initialize, canProcess, process, cleanup)
- Event emission for state updates and notifications
- Configurable cooldown periods and processing rules
- Built-in error handling and recovery mechanisms

**Event Broadcasting**:
- WebSocket-based real-time event distribution
- Structured message protocol for different event types
- Client connection management and message queuing
- Event history and replay capabilities

#### Agent Processing Pipeline

1. **Content Ingestion**: New transcript content triggers agent evaluation
2. **Agent Filtering**: Each agent determines if it should process the content
3. **Parallel Processing**: Eligible agents process content concurrently
4. **State Updates**: Agents emit state changes and events
5. **Event Broadcasting**: Updates are broadcast to connected clients
6. **UI Updates**: Frontend components react to relevant events

### Performance Requirements

- **Agent Processing**: <100ms per agent per content chunk
- **Event Broadcasting**: <50ms from agent event to client notification
- **Memory Usage**: <100MB additional overhead for agent system
- **Concurrent Agents**: Support 10+ agents without performance degradation
- **Session Duration**: Stable operation for 6+ hour sessions

### Error Handling and Recovery

- **Agent Isolation**: Agent failures do not affect other agents or core system
- **Graceful Degradation**: System continues operating with reduced functionality
- **Error Logging**: Comprehensive logging for debugging and monitoring
- **Recovery Mechanisms**: Automatic agent restart and state recovery
- **Fallback Modes**: Manual overrides for critical functionality

## Data Storage Architecture

### Local JSON File-Based Storage

**Architecture Choice**: JSON file-based storage with smart indexing for campaign lore data.

**Rationale**:
- Human-readable and manually editable by DMs
- Version control friendly for campaign evolution tracking
- No external dependencies or database setup required
- Easy backup/restore of entire campaigns
- Flexible schema evolution as requirements emerge
- Aligns with local-only operation requirement

**Directory Structure**:
```
campaign_data/
├── campaign.json          # Campaign metadata and settings
├── npcs/
│   ├── index.json        # NPC registry with quick lookup data
│   └── [npc-id].json     # Individual NPC files with full details
├── locations/
│   ├── index.json        # Location registry
│   └── [location-id].json
├── items/
│   ├── index.json
│   └── [item-id].json
├── quests/
│   ├── index.json
│   └── [quest-id].json
└── sessions/
    └── [session-date].json # Session logs and timeline events
```

**Core Data Manager**:
- `CampaignDataManager` class for entity CRUD operations
- In-memory caching for real-time performance during gameplay
- Smart indexing by name, location, and mention frequency
- Fuzzy matching for transcription error tolerance
- Atomic file operations to prevent corruption

## D&D-Specific Requirements

### Campaign Data Integration

**Character Database**:
- Player character profiles and statistics
- NPC database with relationships and locations
- Campaign-specific terminology and proper nouns

**World Information**:
- Location hierarchy and connections
- Faction relationships and conflicts
- Campaign timeline and historical events

**Rules Integration**:
- Spell and ability recognition
- Combat mechanics and status effects
- Skill check and saving throw detection

### Content Processing Capabilities

**Narrative Analysis**:
- Story beat detection (exposition, rising action, climax, resolution)
- Emotional tone analysis for scene setting
- Pacing recommendations for DM guidance

**Mechanical Support**:
- Initiative tracking and turn management
- Resource usage monitoring (spell slots, abilities)
- Condition and status effect tracking

**World Building**:
- Consistency checking against established lore
- Automatic timeline and event logging
- Relationship mapping between characters and factions

## Non-Functional Requirements

### Reliability
- 99.9% uptime during active gaming sessions
- Automatic recovery from transient failures
- Data persistence across system restarts

### Security
- Local-only operation (no external data transmission)
- Secure handling of campaign data and player information
- File-based storage with appropriate filesystem permissions
- Optional encryption for sensitive campaign content
- No network dependencies for core functionality

### Data Persistence
- Campaign data survives application restarts
- Incremental saves during gameplay to prevent data loss
- Atomic file operations to prevent corruption
- Automatic backup creation before major updates
- Human-readable format allows manual recovery if needed

### Usability
- Zero-configuration startup for basic functionality
- Intuitive web interface for DM and player use
- Minimal learning curve for non-technical users

### Maintainability
- Modular architecture enabling independent agent development
- Comprehensive test coverage for all agent functionality
- Clear documentation and API specifications

## Testing Strategy

### Test-Driven Development Approach

**Unit Testing**:
- Individual agent functionality and edge cases
- State management and event emission
- Error handling and recovery mechanisms

**Integration Testing**:
- Agent system coordination and communication
- WebSocket event broadcasting and client handling
- End-to-end transcript processing workflows

**Performance Testing**:
- Agent processing latency under various loads
- Memory usage during extended sessions
- Concurrent agent execution benchmarks

**Campaign Simulation Testing**:
- Realistic D&D session transcripts for validation
- Edge case scenarios (combat, social encounters, exploration)
- Multi-hour session stability testing

### Quality Assurance

**Regression Prevention**:
- Automated test suite execution on all changes
- Performance benchmark validation
- Campaign data integrity verification

**User Acceptance Testing**:
- Real D&D session validation with target users
- Usability testing with DMs of varying technical skill
- Feedback integration and iterative improvement

## Deployment and Operations

### Development Environment
- Local development with mock transcript generation
- Agent development sandbox with test campaigns
- Performance profiling and optimization tools

### Production Deployment
- Single-machine deployment for campaign use
- Configuration management for different campaign settings
- Backup and recovery procedures for campaign data

### Monitoring and Observability
- Agent performance metrics and health monitoring
- Error tracking and alerting systems
- Usage analytics for feature optimization

## Risk Mitigation

### Technical Risks
- **Agent Performance Degradation**: Implement circuit breakers and performance monitoring
- **Memory Leaks**: Regular memory profiling and automated leak detection
- **WebSocket Connection Issues**: Robust reconnection logic and fallback mechanisms

### Operational Risks
- **Session Disruption**: Comprehensive error handling and graceful degradation
- **Data Loss**: Regular state persistence and backup mechanisms
- **User Adoption**: Intuitive design and comprehensive documentation

### Mitigation Strategies
- Phased rollout with extensive testing at each stage
- Fallback to manual operation if automated systems fail
- Regular user feedback collection and rapid iteration cycles