# Agent API Conventions

## Overview

This document defines the standardized API conventions for agent interaction with the CampaignDataManager in the lorecat TTRPG assistant. These conventions ensure consistency, performance, and maintainability across all agent implementations.

**Status**: ✅ **SPECIFICATION COMPLETE** - Ready for implementation  
**Dependencies**: CampaignDataManager (implemented), BaseAgent (planned)

## Core Design Principles

1. **Dependency Injection** - CampaignDataManager injected into agent constructors
2. **Async-First** - All data operations are asynchronous
3. **Event-Driven** - Agents communicate through events, not direct calls
4. **Error Resilient** - Standardized error handling with graceful degradation
5. **Performance Optimized** - Multi-level caching strategy
6. **Type Consistent** - Standardized entity schemas and validation

## 1. Agent Constructor Pattern

### Standard Agent Structure
```javascript
class MyAgent extends BaseAgent {
  constructor(dataManager) {
    super();
    this.dataManager = dataManager;  // Injected dependency
    this.agentCache = new Map();     // Agent-specific caching
    this.isInitialized = false;
  }

  async initialize() {
    // Load domain-specific data
    // Build agent caches/indexes
    // Subscribe to events
    // Emit 'agentReady' when complete
    this.isInitialized = true;
    this.emit('agentReady', { agentType: this.constructor.name });
  }

  async process(content) {
    if (!this.isInitialized) {
      throw new Error('Agent not initialized');
    }
    // Process content and interact with dataManager
  }

  cleanup() {
    // Remove event listeners
    // Clear caches
    this.removeAllListeners();
  }
}
```

### Dependency Injection
- **CampaignDataManager** is injected via constructor
- Agents **MUST NOT** create their own data manager instances
- Enables testing with mock data managers
- Ensures single source of truth for campaign data

## 2. Entity Management API

### CRUD Operations
All agents use the same standardized interface for entity management:

```javascript
// CREATE/UPDATE entities
const savedEntity = await this.dataManager.saveEntity(entityType, entityId, entityData);

// READ operations
const entity = await this.dataManager.loadEntity(entityType, entityId);
const allEntities = await this.dataManager.getAllEntities(entityType);

// DELETE operations
await this.dataManager.deleteEntity(entityType, entityId);
```

### Supported Entity Types
- `'npcs'` - Non-player characters
- `'locations'` - Places and areas
- `'items'` - Equipment, artifacts, consumables
- `'quests'` - Missions and objectives
- `'factions'` - Organizations and groups

### Entity Validation
Agents **MUST** ensure entities have required fields:
```javascript
// Required for all entities
const entityData = {
  id: entityId,           // Must match the entityId parameter
  name: 'Entity Name',    // Human-readable name
  // ... entity-specific fields
};
```

## 3. Search & Discovery API

### Name-Based Search
```javascript
// Exact search (case-insensitive)
const results = await this.dataManager.findEntitiesByName('Gandalf');

// Fuzzy search for transcription errors
const fuzzyResults = await this.dataManager.findEntitiesByName('Gandlf', true);
```

### Contextual Search
```javascript
// Find entities by location
const entitiesInLocation = await this.dataManager.findEntitiesByLocation('tavern-id');

// Find entities by tags
const taggedEntities = await this.dataManager.findEntitiesByTag('important');
```

### Search Best Practices
- Use **fuzzy search** for user input or transcription data
- Use **exact search** for programmatic lookups
- **Cache search results** when appropriate for performance
- **Validate search parameters** before calling data manager

## 4. Real-time Tracking API

### Entity Mention Tracking
```javascript
// Update mention count and timeline
await this.dataManager.updateEntityMention(entityType, entityId, {
  timestamp: new Date().toISOString(),
  context: 'Descriptive context of the mention'
});
```

### Session Event Logging
```javascript
// Log significant gameplay events
await this.dataManager.logSessionEvent({
  type: 'location_change|combat_start|npc_interaction|quest_update',
  entityType: entityType,     // Optional: related entity type
  entityId: entityId,         // Optional: related entity ID
  context: 'Event description',
  agentId: this.constructor.name,  // Agent attribution
  metadata: {                 // Optional: additional data
    // Agent-specific metadata
  }
});
```

### Event Types Convention
- `'location_change'` - Party moves to new location
- `'combat_start'` / `'combat_end'` - Combat state changes
- `'npc_interaction'` - Significant NPC dialogue/interaction
- `'quest_update'` - Quest progress or status change
- `'item_acquired'` / `'item_lost'` - Inventory changes
- `'agent_detection'` - Agent-specific detections

## 5. Event-Driven Integration

### Listening to Data Manager Events
```javascript
class NPCTracker extends BaseAgent {
  async initialize() {
    // Subscribe to relevant data events
    this.dataManager.on('entitySaved', this.handleEntitySaved.bind(this));
    this.dataManager.on('entityMentioned', this.handleEntityMentioned.bind(this));
    this.dataManager.on('sessionEventLogged', this.handleSessionEvent.bind(this));
    
    // Load initial data
    await this.loadExistingNPCs();
    
    this.emit('agentReady', { agentType: 'NPCTracker' });
  }

  handleEntitySaved({ entityType, entityId, entityData }) {
    if (entityType === 'npcs') {
      this.updateNPCCache(entityId, entityData);
    }
  }

  handleEntityMentioned({ entityType, entityId, context }) {
    if (entityType === 'npcs') {
      this.emit('npcMentioned', { entityId, context });
    }
  }
}
```

### Available Data Manager Events
- `'campaignInitialized'` - New campaign created
- `'campaignLoaded'` - Existing campaign loaded
- `'entitySaved'` - Entity created or updated
- `'entityDeleted'` - Entity removed
- `'entityMentioned'` - Entity mention tracked
- `'sessionEventLogged'` - Session event recorded
- `'indexesRebuilt'` - Search indexes refreshed
- `'backupCreated'` - Campaign backup created
- `'backupRestored'` - Campaign restored from backup

## 6. Agent Initialization Pattern

### Standard Initialization Flow
```javascript
class LocationTracker extends BaseAgent {
  async initialize() {
    try {
      // 1. Load existing entities for agent's domain
      const locations = await this.dataManager.getAllEntities('locations');
      
      // 2. Build agent-specific indexes/caches
      this.locationKeywords = new Map();
      this.locationAliases = new Map();
      
      locations.forEach(location => {
        // Index by name
        this.locationKeywords.set(location.name.toLowerCase(), location.id);
        
        // Index by aliases
        location.aliases?.forEach(alias => {
          this.locationAliases.set(alias.toLowerCase(), location.id);
        });
      });
      
      // 3. Subscribe to relevant events
      this.dataManager.on('entitySaved', this.handleLocationUpdate.bind(this));
      
      // 4. Mark as initialized
      this.isInitialized = true;
      this.emit('agentReady', { 
        agentType: 'LocationTracker',
        entitiesLoaded: locations.length 
      });
      
    } catch (error) {
      this.emit('agentError', {
        agentType: 'LocationTracker',
        phase: 'initialization',
        error: error.message
      });
      throw error;
    }
  }
}
```

### Initialization Requirements
- **Load domain data** relevant to the agent
- **Build performance caches** for real-time processing
- **Subscribe to events** for data synchronization
- **Emit 'agentReady'** when initialization complete
- **Handle initialization errors** gracefully

## 7. Content Processing Pattern

### Standard Processing Flow
```javascript
class NPCTracker extends BaseAgent {
  async process(content) {
    if (!this.isInitialized) {
      throw new Error('NPCTracker not initialized');
    }

    try {
      // 1. Extract relevant information from content
      const mentions = this.detectNPCMentions(content);
      
      // 2. Process each detection
      for (const mention of mentions) {
        await this.processNPCMention(mention);
      }
      
    } catch (error) {
      this.emit('agentError', {
        agentType: 'NPCTracker',
        phase: 'processing',
        error: error.message,
        content: content.substring(0, 100) // First 100 chars for context
      });
    }
  }

  async processNPCMention(mention) {
    try {
      // Check if NPC exists
      const npc = await this.dataManager.loadEntity('npcs', mention.npcId);
      
      // Update mention tracking
      await this.dataManager.updateEntityMention('npcs', mention.npcId, {
        timestamp: mention.timestamp,
        context: mention.context
      });
      
      // Emit agent-specific event
      this.emit('npcMentioned', { 
        npcId: mention.npcId, 
        npc: npc,
        context: mention.context 
      });
      
    } catch (error) {
      if (error.message.includes('Entity not found')) {
        // Create new NPC if not found
        await this.createNewNPC(mention);
      } else {
        throw error;
      }
    }
  }
}
```

### Processing Requirements
- **Validate initialization** before processing
- **Extract relevant data** from content efficiently
- **Handle entity creation** for new discoveries
- **Update entity mentions** for tracking
- **Emit specific events** for UI updates
- **Handle processing errors** without crashing

## 8. Error Handling Convention

### Standardized Error Handling
```javascript
class BaseAgent extends EventEmitter {
  async safeDataOperation(operation, fallback = null) {
    try {
      return await operation();
    } catch (error) {
      this.emit('agentError', {
        agentType: this.constructor.name,
        operation: operation.name || 'unknown',
        error: error.message,
        timestamp: new Date().toISOString()
      });

      return fallback;
    }
  }

  async safeEntityLoad(entityType, entityId, fallback = null) {
    return this.safeDataOperation(
      () => this.dataManager.loadEntity(entityType, entityId),
      fallback
    );
  }
}

// Usage in agents
const npc = await this.safeEntityLoad('npcs', npcId, null);
if (!npc) {
  // Handle missing entity gracefully
  await this.createNewNPC({ id: npcId, name: 'Unknown NPC' });
}
```

### Error Event Standards
All agents **MUST** emit standardized error events:
```javascript
this.emit('agentError', {
  agentType: this.constructor.name,    // Agent class name
  phase: 'initialization|processing|cleanup',  // Error phase
  operation: 'loadEntity|saveEntity|search',   // Specific operation
  error: error.message,                // Error description
  timestamp: new Date().toISOString(), // When error occurred
  context: { /* additional context */ } // Optional context data
});
```

### Error Recovery Strategies
- **Graceful Degradation** - Continue operation with reduced functionality
- **Retry Logic** - Retry failed operations with exponential backoff
- **Fallback Values** - Use default/cached values when operations fail
- **Error Isolation** - Prevent agent errors from affecting other agents

## 9. Performance Optimization Pattern

### Multi-Level Caching Strategy
```javascript
class NPCTracker extends BaseAgent {
  constructor(dataManager) {
    super();
    this.dataManager = dataManager;

    // Agent-level caches for performance
    this.npcCache = new Map();          // Full NPC objects
    this.aliasToId = new Map();         // Alias -> ID mapping
    this.nameToId = new Map();          // Name -> ID mapping
    this.lastCacheUpdate = null;
  }

  async getOrLoadNPC(npcId) {
    // 1. Check agent cache first (fastest)
    if (this.npcCache.has(npcId)) {
      return this.npcCache.get(npcId);
    }

    // 2. Load from data manager (has its own cache)
    const npc = await this.dataManager.loadEntity('npcs', npcId);

    // 3. Cache in agent for faster subsequent access
    this.npcCache.set(npcId, npc);

    // 4. Update alias mappings
    this.updateAliasMappings(npc);

    return npc;
  }

  updateAliasMappings(npc) {
    // Update name mapping
    this.nameToId.set(npc.name.toLowerCase(), npc.id);

    // Update alias mappings
    npc.aliases?.forEach(alias => {
      this.aliasToId.set(alias.toLowerCase(), npc.id);
    });
  }

  // Fast lookup without loading full entity
  findNPCIdByName(name) {
    const normalizedName = name.toLowerCase();
    return this.nameToId.get(normalizedName) ||
           this.aliasToId.get(normalizedName);
  }
}
```

### Performance Best Practices
- **Cache frequently accessed data** at the agent level
- **Use lightweight lookups** before full entity loads
- **Batch operations** when possible to reduce I/O
- **Lazy load** entity details only when needed
- **Clear caches** when entities are updated externally

## 10. Agent Registration API

### TailAgentSystem Integration
```javascript
// Proposed agent system integration pattern
class TailAgentSystem {
  constructor(dataManager) {
    this.dataManager = dataManager;
    this.agents = new Map();
    this.isRunning = false;
  }

  async registerAgent(AgentClass, config = {}) {
    // 1. Create agent with dependency injection
    const agent = new AgentClass(this.dataManager);

    // 2. Apply configuration
    if (config.settings) {
      Object.assign(agent.settings, config.settings);
    }

    // 3. Initialize agent
    await agent.initialize();

    // 4. Register for content processing
    this.agents.set(agent.constructor.name, agent);

    // 5. Set up error handling
    agent.on('agentError', this.handleAgentError.bind(this));

    return agent;
  }

  async processContent(content) {
    const promises = [];

    for (const [agentName, agent] of this.agents) {
      if (agent.canProcess && !agent.canProcess(content)) {
        continue; // Skip if agent can't process this content
      }

      // Process in parallel for performance
      promises.push(
        agent.process(content).catch(error => {
          console.error(`Agent ${agentName} processing failed:`, error);
          // Don't let one agent failure stop others
        })
      );
    }

    await Promise.all(promises);
  }
}

// Usage example
const agentSystem = new TailAgentSystem(dataManager);

// Register agents with optional configuration
await agentSystem.registerAgent(NPCTracker, {
  settings: { fuzzyThreshold: 0.8 }
});

await agentSystem.registerAgent(LocationTracker, {
  settings: { keywordSensitivity: 'high' }
});

await agentSystem.registerAgent(CombatTracker);
```

### Registration Requirements
- **Dependency injection** of CampaignDataManager
- **Initialization** before registration completion
- **Error handling** setup for agent failures
- **Configuration** support for agent customization
- **Parallel processing** capability for performance

## 11. Data Schema Conventions

### Entity Creation Standards

#### NPCs
```javascript
await dataManager.saveEntity('npcs', npcId, {
  id: npcId,                          // Required: unique identifier
  name: 'Character Name',             // Required: display name
  aliases: ['Alias1', 'Alias2'],      // Optional: alternative names
  location: 'current-location-id',    // Optional: current location
  status: 'alive|dead|unknown',       // Optional: vital status
  relationship: 'ally|enemy|neutral', // Optional: party relationship
  importance: 'major|minor|background', // Optional: narrative importance
  description: 'Character description', // Optional: physical/background
  notes: 'DM notes about character',   // Optional: private DM notes
  tags: ['tag1', 'tag2'],             // Optional: categorization
  mentionCount: 0,                    // Auto-managed: mention tracking
  firstMentioned: null,               // Auto-managed: first mention time
  lastMentioned: null,                // Auto-managed: last mention time
  timeline: [],                       // Auto-managed: mention history
  relationships: {                    // Optional: relationships to other entities
    'other-npc-id': {
      type: 'family|friend|enemy|business',
      role: 'specific relationship role',
      strength: 1-10                  // Relationship strength
    }
  }
});
```

#### Locations
```javascript
await dataManager.saveEntity('locations', locationId, {
  id: locationId,                     // Required: unique identifier
  name: 'Location Name',              // Required: display name
  aliases: ['Alias1', 'Alias2'],      // Optional: alternative names
  type: 'settlement|dungeon|wilderness|building', // Optional: location type
  parentLocation: 'parent-location-id', // Optional: hierarchical parent
  description: 'Location description', // Optional: detailed description
  tags: ['tag1', 'tag2'],             // Optional: categorization
  visitCount: 0,                      // Auto-managed: visit tracking
  firstVisited: null,                 // Auto-managed: first visit time
  lastVisited: null,                  // Auto-managed: last visit time
  currentlyAt: false,                 // Auto-managed: party presence
  timeline: [],                       // Auto-managed: visit history
  pointsOfInterest: [                 // Optional: notable features
    {
      name: 'Feature Name',
      description: 'Feature description'
    }
  ],
  npcsPresent: ['npc-id-1', 'npc-id-2'] // Optional: NPCs at location
});
```

#### Items
```javascript
await dataManager.saveEntity('items', itemId, {
  id: itemId,                         // Required: unique identifier
  name: 'Item Name',                  // Required: display name
  aliases: ['Alias1', 'Alias2'],      // Optional: alternative names
  type: 'weapon|armor|consumable|treasure|quest', // Optional: item type
  rarity: 'common|uncommon|rare|very rare|legendary', // Optional: D&D rarity
  description: 'Item description',     // Optional: detailed description
  location: 'current-location-id',    // Optional: current location
  owner: 'character-id',              // Optional: current owner
  tags: ['tag1', 'tag2'],             // Optional: categorization
  mentionCount: 0,                    // Auto-managed: mention tracking
  timeline: [],                       // Auto-managed: mention history
  properties: {                       // Optional: item-specific properties
    damage: '1d8',
    ac: 15,
    charges: 3
  }
});
```

### Schema Validation
Agents **SHOULD** validate entity data before saving:
```javascript
validateNPCData(npcData) {
  if (!npcData.id || !npcData.name) {
    throw new Error('NPC must have id and name fields');
  }

  if (npcData.status && !['alive', 'dead', 'unknown'].includes(npcData.status)) {
    throw new Error('Invalid NPC status');
  }

  // Additional validation as needed
  return true;
}
```

## 12. Testing Conventions

### Agent Testing Pattern
```javascript
import { CampaignDataManager } from '../src/campaign-data-manager.js';
import { NPCTracker } from '../src/agents/npc-tracker.js';

describe('NPCTracker Agent', () => {
  let dataManager;
  let agent;
  let tempCampaignPath;

  beforeEach(async () => {
    // Create test campaign
    tempCampaignPath = './test-campaign-' + Date.now();
    dataManager = new CampaignDataManager(tempCampaignPath);
    await dataManager.initializeCampaign({ name: 'Test Campaign' });

    // Create agent with test data manager
    agent = new NPCTracker(dataManager);
    await agent.initialize();
  });

  afterEach(async () => {
    // Cleanup
    agent.cleanup();
    dataManager.close();
    await fs.rm(tempCampaignPath, { recursive: true, force: true });
  });

  test('should detect NPC mentions', async () => {
    // Add test NPC
    await dataManager.saveEntity('npcs', 'gandalf', {
      id: 'gandalf',
      name: 'Gandalf',
      aliases: ['Gandalf the Grey']
    });

    // Test content processing
    await agent.process('Gandalf arrives at the tavern');

    // Verify mention was tracked
    const npc = await dataManager.loadEntity('npcs', 'gandalf');
    expect(npc.mentionCount).toBe(1);
  });
});
```

This comprehensive API convention ensures consistent, performant, and maintainable agent development while providing clear patterns for common operations and error handling.
