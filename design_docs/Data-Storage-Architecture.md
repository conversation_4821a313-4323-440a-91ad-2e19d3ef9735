# Lorecat Data Storage Architecture

## Overview

This document defines the local JSON file-based storage architecture for campaign lore data in the lorecat TTRPG assistant. The design prioritizes human readability, local-only operation, and real-time performance during gameplay.

**Status**: ✅ **IMPLEMENTED** - Full CampaignDataManager with comprehensive test suite

**Implementation**: `implementation/src/campaign-data-manager.js`
**Tests**: `implementation/tests/unit/campaign-data-manager.test.js` (31 tests, 100% pass rate)
**Demo**: `implementation/campaign-demo.js`

## Architecture Decision

### Chosen Approach: JSON File-Based Storage with Smart Indexing

**Rationale:**
- **Human-readable**: DMs can manually edit campaign data between sessions
- **Version control friendly**: Campaign evolution can be tracked in git
- **No external dependencies**: Aligns with local-only operation requirement
- **Easy backup/restore**: Simple file copying for campaign backups
- **Flexible schema**: Easy to evolve as requirements emerge during playtesting
- **Performance**: In-memory caching provides real-time lookup speeds

## Directory Structure

```
campaign_data/
├── campaign.json              # Campaign metadata and settings
├── npcs/
│   ├── index.json            # NPC registry with quick lookup data
│   └── [npc-id].json         # Individual NPC files with full details
├── locations/
│   ├── index.json            # Location registry
│   └── [location-id].json    # Individual location files
├── items/
│   ├── index.json            # Item registry
│   └── [item-id].json        # Individual item files
├── quests/
│   ├── index.json            # Quest registry
│   └── [quest-id].json       # Individual quest files
├── factions/
│   ├── index.json            # Faction registry
│   └── [faction-id].json     # Individual faction files
└── sessions/
    └── [session-date].json   # Session logs and timeline events
```

## Schema Definitions

### Campaign Metadata
```json
{
  "id": "curse-of-strahd-2024",
  "name": "Curse of Strahd",
  "created": "2024-01-15T10:00:00Z",
  "lastSession": "2024-08-19T19:00:00Z",
  "currentSession": "2024-08-19",
  "settings": {
    "transcriptionFile": "/path/to/session.log",
    "autoSave": true,
    "backupFrequency": "hourly",
    "fuzzyMatchThreshold": 0.8
  },
  "playerCharacters": [
    {
      "id": "pc-aeliana",
      "name": "Aeliana",
      "class": "Ranger",
      "player": "Alice"
    }
  ]
}
```

### NPC Schema
```json
{
  "id": "barovia-burgomaster",
  "name": "Ismark Kolyanovich",
  "aliases": ["Ismark", "Burgomaster", "Ismark the Lesser"],
  "location": "village-of-barovia",
  "status": "alive",
  "relationship": "ally",
  "importance": "major",
  "firstMentioned": "2024-08-19T19:15:00Z",
  "lastMentioned": "2024-08-19T20:45:00Z",
  "mentionCount": 12,
  "description": "The burgomaster of the Village of Barovia",
  "notes": "Wants party to help rescue his sister Ireena from Strahd",
  "tags": ["important", "quest-giver", "barovia"],
  "relationships": {
    "ireena-kolyana": {
      "type": "family",
      "role": "brother",
      "strength": 10
    },
    "strahd": {
      "type": "enemy",
      "role": "oppressor",
      "strength": 8
    }
  },
  "timeline": [
    {
      "timestamp": "2024-08-19T19:15:00Z",
      "event": "first_mention",
      "context": "The burgomaster approaches the party at the tavern"
    },
    {
      "timestamp": "2024-08-19T19:30:00Z",
      "event": "quest_given",
      "context": "Asks party to escort Ireena to Vallaki"
    }
  ]
}
```

### Location Schema
```json
{
  "id": "village-of-barovia",
  "name": "Village of Barovia",
  "aliases": ["Barovia", "the village"],
  "type": "settlement",
  "parentLocation": "barovia-valley",
  "description": "A gloomy village shrouded in mist",
  "firstVisited": "2024-08-19T19:00:00Z",
  "lastVisited": "2024-08-19T21:00:00Z",
  "visitCount": 3,
  "currentlyAt": true,
  "tags": ["settlement", "starting-location"],
  "npcsPresent": ["barovia-burgomaster", "ireena-kolyana"],
  "pointsOfInterest": [
    {
      "name": "Blood on the Vine Tavern",
      "description": "The local tavern where the party first met Ismark"
    },
    {
      "name": "Burgomaster's House",
      "description": "Ismark's home where Ireena is staying"
    }
  ],
  "timeline": [
    {
      "timestamp": "2024-08-19T19:00:00Z",
      "event": "arrival",
      "context": "Party arrives in the village as the mists part"
    }
  ]
}
```

## Core Data Manager Implementation

### CampaignDataManager Class
```javascript
class CampaignDataManager {
  constructor(campaignPath) {
    this.campaignPath = campaignPath;
    this.cache = new Map();           // Entity cache for performance
    this.indexes = {
      byName: new Map(),              // Quick name lookups
      byLocation: new Map(),          // Entities by location
      byMentionFrequency: new Map(),  // Most mentioned entities
      byTag: new Map()                // Entities by tag
    };
    this.campaign = null;
  }

  // Core CRUD operations
  async loadEntity(type, id) { /* ... */ }
  async saveEntity(type, id, data) { /* ... */ }
  async deleteEntity(type, id) { /* ... */ }
  async getAllEntities(type) { /* ... */ }

  // Search and query operations
  async findEntitiesByName(query, fuzzy = true) { /* ... */ }
  async findEntitiesByLocation(locationId) { /* ... */ }
  async findEntitiesByTag(tag) { /* ... */ }

  // Real-time update operations
  async updateEntityMention(type, id, context) { /* ... */ }
  async logSessionEvent(event) { /* ... */ }

  // Index management
  rebuildIndexes() { /* ... */ }
  updateIndexes(type, id, data) { /* ... */ }
}
```

## Integration with Agent System

**API Specification**: See `Agent-API-Conventions.md` for complete agent integration patterns and standards.

### NPCTracker Agent Example
```javascript
class NPCTracker extends BaseAgent {
  constructor(dataManager) {
    super();
    this.dataManager = dataManager;
    this.knownNPCs = new Map();  // Fast lookup cache
  }

  async initialize() {
    // Load all NPCs into memory for real-time processing
    const npcs = await this.dataManager.getAllEntities('npcs');
    npcs.forEach(npc => {
      npc.aliases.forEach(alias => {
        this.knownNPCs.set(alias.toLowerCase(), npc.id);
      });
    });
  }

  async process(content) {
    const mentions = this.detectNPCMentions(content);
    
    for (const mention of mentions) {
      await this.updateNPCMention(mention);
    }
  }

  async updateNPCMention(mention) {
    await this.dataManager.updateEntityMention('npcs', mention.npcId, {
      timestamp: new Date().toISOString(),
      context: mention.context
    });
    
    // Emit real-time event for UI updates
    this.emit('npcMentioned', { 
      npcId: mention.npcId, 
      context: mention.context 
    });
  }
}
```

## Performance Considerations

### In-Memory Caching Strategy
- Load frequently accessed entities into memory on startup
- Cache all entity names and aliases for fast pattern matching
- Lazy load full entity details only when needed
- Implement cache invalidation on file updates

### File Operation Optimization
- Use atomic writes to prevent corruption during saves
- Batch multiple updates into single file operations
- Implement write-through caching for immediate consistency
- Use file watching to detect external changes

### Indexing Strategy
- Maintain in-memory indexes for common query patterns
- Rebuild indexes on startup and after bulk operations
- Update indexes incrementally for single entity changes
- Store index snapshots for faster startup times

## Backup and Recovery

### Automatic Backups
- Create timestamped backups before each session
- Implement rolling backup retention (keep last 10 sessions)
- Backup on significant campaign milestones
- Export functionality for manual backups

### Recovery Procedures
- Validate JSON integrity on startup
- Automatic recovery from backup if corruption detected
- Manual recovery tools for partial data loss
- Import/export for campaign migration

## Future Extensibility

### Schema Evolution
- Version campaign data format for backward compatibility
- Migration scripts for schema updates
- Graceful handling of unknown fields
- Documentation of schema changes

### Additional Entity Types
- Spells and abilities
- Magic items with history
- Faction relationships
- Timeline events
- Combat encounters

This architecture provides a solid foundation for the lorecat project while maintaining the flexibility to evolve as real-world usage reveals additional requirements.
