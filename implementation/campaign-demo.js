#!/usr/bin/env node

/**
 * Campaign Data Manager Demo
 * Demonstrates the CampaignDataManager functionality with sample D&D data
 */

import { CampaignDataManager } from './src/campaign-data-manager.js';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runDemo() {
  console.log('🎲 Campaign Data Manager Demo\n');

  // Create demo campaign directory
  const demoPath = path.join(__dirname, 'demo-campaign');
  
  // Clean up any existing demo
  try {
    await fs.rm(demoPath, { recursive: true, force: true });
  } catch (error) {
    // Ignore if directory doesn't exist
  }

  const dataManager = new CampaignDataManager(demoPath);

  try {
    // 1. Initialize a new campaign
    console.log('📝 Initializing new campaign...');
    const campaign = await dataManager.initializeCampaign({
      name: 'Curse of Strahd Demo',
      transcriptionFile: './session-transcript.log',
      settings: {
        autoSave: true,
        fuzzyMatchThreshold: 0.8
      },
      playerCharacters: [
        { id: 'pc-aeliana', name: 'Aeliana', class: 'Ranger', player: '<PERSON>' },
        { id: 'pc-thorin', name: 'Thorin', class: 'Fighter', player: 'Bob' },
        { id: 'pc-lyra', name: 'Lyra', class: 'Wizard', player: 'Carol' }
      ]
    });
    console.log(`✅ Campaign "${campaign.name}" created with ID: ${campaign.id}\n`);

    // 2. Add some NPCs
    console.log('👥 Adding NPCs...');
    const npcs = [
      {
        id: 'ismark-kolyanovich',
        name: 'Ismark Kolyanovich',
        aliases: ['Ismark', 'Burgomaster', 'Ismark the Lesser'],
        location: 'village-of-barovia',
        status: 'alive',
        relationship: 'ally',
        importance: 'major',
        description: 'The burgomaster of the Village of Barovia',
        notes: 'Wants party to help rescue his sister Ireena from Strahd',
        tags: ['important', 'quest-giver', 'barovia'],
        mentionCount: 0,
        timeline: []
      },
      {
        id: 'ireena-kolyana',
        name: 'Ireena Kolyana',
        aliases: ['Ireena', 'Tatyana'],
        location: 'village-of-barovia',
        status: 'alive',
        relationship: 'ally',
        importance: 'major',
        description: 'Ismark\'s sister, bears resemblance to Tatyana',
        notes: 'Strahd is obsessed with her, needs protection',
        tags: ['important', 'protected', 'barovia'],
        mentionCount: 0,
        timeline: []
      },
      {
        id: 'strahd-von-zarovich',
        name: 'Strahd von Zarovich',
        aliases: ['Strahd', 'Count Strahd', 'The Devil Strahd'],
        location: 'castle-ravenloft',
        status: 'undead',
        relationship: 'enemy',
        importance: 'major',
        description: 'The vampire lord of Barovia',
        notes: 'Ancient vampire, ruler of the domain, obsessed with Ireena',
        tags: ['boss', 'vampire', 'important', 'enemy'],
        mentionCount: 0,
        timeline: []
      }
    ];

    for (const npc of npcs) {
      await dataManager.saveEntity('npcs', npc.id, npc);
      console.log(`  ✅ Added NPC: ${npc.name}`);
    }
    console.log();

    // 3. Add some locations
    console.log('🏰 Adding locations...');
    const locations = [
      {
        id: 'village-of-barovia',
        name: 'Village of Barovia',
        aliases: ['Barovia', 'the village'],
        type: 'settlement',
        parentLocation: 'barovia-valley',
        description: 'A gloomy village shrouded in mist',
        tags: ['settlement', 'starting-location'],
        visitCount: 0,
        timeline: []
      },
      {
        id: 'castle-ravenloft',
        name: 'Castle Ravenloft',
        aliases: ['Ravenloft', 'Strahd\'s Castle'],
        type: 'castle',
        parentLocation: 'barovia-valley',
        description: 'The imposing castle of Count Strahd',
        tags: ['castle', 'dungeon', 'important'],
        visitCount: 0,
        timeline: []
      }
    ];

    for (const location of locations) {
      await dataManager.saveEntity('locations', location.id, location);
      console.log(`  ✅ Added location: ${location.name}`);
    }
    console.log();

    // 4. Simulate some mentions during gameplay
    console.log('🎮 Simulating gameplay mentions...');
    
    // Ismark is mentioned
    await dataManager.updateEntityMention('npcs', 'ismark-kolyanovich', {
      timestamp: new Date().toISOString(),
      context: 'The burgomaster approaches the party at the tavern'
    });
    console.log('  📢 Ismark mentioned in tavern scene');

    // Ireena is mentioned
    await dataManager.updateEntityMention('npcs', 'ireena-kolyana', {
      timestamp: new Date().toISOString(),
      context: 'Ismark speaks of his sister who needs protection'
    });
    console.log('  📢 Ireena mentioned by Ismark');

    // Strahd is mentioned
    await dataManager.updateEntityMention('npcs', 'strahd-von-zarovich', {
      timestamp: new Date().toISOString(),
      context: 'The villagers whisper fearfully of Count Strahd'
    });
    console.log('  📢 Strahd mentioned by villagers');

    // Log a session event
    await dataManager.logSessionEvent({
      type: 'location_change',
      entityType: 'location',
      entityId: 'village-of-barovia',
      context: 'Party arrives in the Village of Barovia as the mists part'
    });
    console.log('  📝 Session event logged: arrival in Barovia\n');

    // 5. Demonstrate search functionality
    console.log('🔍 Testing search functionality...');
    
    // Search by name
    const ismarkResults = await dataManager.findEntitiesByName('Ismark');
    console.log(`  🎯 Found ${ismarkResults.length} entities for "Ismark": ${ismarkResults.map(e => e.name).join(', ')}`);

    // Search by alias
    const burgomasterResults = await dataManager.findEntitiesByName('Burgomaster');
    console.log(`  🎯 Found ${burgomasterResults.length} entities for "Burgomaster": ${burgomasterResults.map(e => e.name).join(', ')}`);

    // Fuzzy search (simulating transcription error)
    const fuzzyResults = await dataManager.findEntitiesByName('Ireana', true); // Misspelled "Ireena"
    console.log(`  🎯 Fuzzy search for "Ireana" found: ${fuzzyResults.map(e => e.name).join(', ')}`);

    // Search by tag
    const importantEntities = await dataManager.findEntitiesByTag('important');
    console.log(`  🏷️  Found ${importantEntities.length} important entities: ${importantEntities.map(e => e.name).join(', ')}`);

    // Search by location
    const baroviaEntities = await dataManager.findEntitiesByLocation('village-of-barovia');
    console.log(`  📍 Found ${baroviaEntities.length} entities in Barovia: ${baroviaEntities.map(e => e.name).join(', ')}\n`);

    // 6. Show campaign statistics
    console.log('📊 Campaign Statistics:');
    const stats = await dataManager.getCampaignStats();
    console.log(`  📈 Total entities: ${stats.totalEntities}`);
    console.log(`  👥 NPCs: ${stats.entityCounts.npcs}`);
    console.log(`  🏰 Locations: ${stats.entityCounts.locations}`);
    console.log(`  💾 Cache size: ${stats.cacheSize} entities`);
    console.log(`  🔍 Index sizes: ${JSON.stringify(stats.indexSizes, null, 2)}\n`);

    // 7. Create a backup
    console.log('💾 Creating backup...');
    const backupName = await dataManager.createBackup();
    console.log(`  ✅ Backup created: ${backupName}\n`);

    // 8. Show file structure
    console.log('📁 Generated file structure:');
    await showDirectoryStructure(demoPath, '');

    console.log('\n🎉 Demo completed successfully!');
    console.log(`📂 Demo files created in: ${demoPath}`);
    console.log('🔍 You can explore the JSON files to see the campaign data structure.');

  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    console.error(error.stack);
  } finally {
    dataManager.close();
  }
}

async function showDirectoryStructure(dirPath, indent = '') {
  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });
    
    for (const entry of entries.sort((a, b) => {
      // Directories first, then files
      if (a.isDirectory() && !b.isDirectory()) return -1;
      if (!a.isDirectory() && b.isDirectory()) return 1;
      return a.name.localeCompare(b.name);
    })) {
      const icon = entry.isDirectory() ? '📁' : '📄';
      console.log(`${indent}${icon} ${entry.name}`);
      
      if (entry.isDirectory() && !entry.name.startsWith('.')) {
        await showDirectoryStructure(path.join(dirPath, entry.name), indent + '  ');
      }
    }
  } catch (error) {
    console.log(`${indent}❌ Error reading directory: ${error.message}`);
  }
}

// Run the demo
runDemo().catch(console.error);
