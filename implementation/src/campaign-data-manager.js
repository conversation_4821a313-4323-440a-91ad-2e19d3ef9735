/**
 * Campaign Data Manager
 * Handles local JSON file-based storage for TTRPG campaign lore data
 * Provides CRUD operations, caching, and indexing for real-time performance
 */

import fs from 'fs/promises';
import path from 'path';
import { EventEmitter } from 'events';

/**
 * Manages campaign data storage with JSON files and smart indexing
 */
export class CampaignDataManager extends EventEmitter {
  constructor(campaignPath) {
    super();
    this.campaignPath = campaignPath;
    this.cache = new Map();           // Entity cache for performance
    this.indexes = {
      byName: new Map(),              // Quick name lookups (name -> [entityIds])
      byLocation: new Map(),          // Entities by location (locationId -> [entityIds])
      byMentionFrequency: new Map(),  // Most mentioned entities (frequency -> [entityIds])
      byTag: new Map()                // Entities by tag (tag -> [entityIds])
    };
    this.campaign = null;
    this.isInitialized = false;
    
    // Entity types and their directories
    this.entityTypes = ['npcs', 'locations', 'items', 'quests', 'factions'];
  }

  /**
   * Initialize a new campaign with default structure
   */
  async initializeCampaign(campaignData) {
    if (!this.campaignPath || typeof this.campaignPath !== 'string' || this.campaignPath.trim() === '') {
      throw new Error('Invalid campaign path');
    }

    // Validate required campaign data
    if (!campaignData.name) {
      throw new Error('Campaign name is required');
    }

    // Create campaign directory
    await fs.mkdir(this.campaignPath, { recursive: true });

    // Create entity type directories
    for (const entityType of this.entityTypes) {
      const entityDir = path.join(this.campaignPath, entityType);
      await fs.mkdir(entityDir, { recursive: true });
      
      // Create empty index file
      const indexPath = path.join(entityDir, 'index.json');
      await fs.writeFile(indexPath, JSON.stringify({ entities: [] }, null, 2));
    }

    // Create sessions directory
    const sessionsDir = path.join(this.campaignPath, 'sessions');
    await fs.mkdir(sessionsDir, { recursive: true });

    // Create backups directory
    const backupsDir = path.join(this.campaignPath, 'backups');
    await fs.mkdir(backupsDir, { recursive: true });

    // Create campaign metadata
    const campaign = {
      id: campaignData.id || `campaign-${Date.now()}`,
      name: campaignData.name,
      created: new Date().toISOString(),
      lastSession: null,
      currentSession: null,
      settings: {
        transcriptionFile: campaignData.transcriptionFile || '',
        autoSave: campaignData.autoSave !== false,
        backupFrequency: campaignData.backupFrequency || 'hourly',
        fuzzyMatchThreshold: campaignData.fuzzyMatchThreshold || 0.8,
        ...campaignData.settings
      },
      playerCharacters: campaignData.playerCharacters || []
    };

    // Save campaign.json
    const campaignFile = path.join(this.campaignPath, 'campaign.json');
    await fs.writeFile(campaignFile, JSON.stringify(campaign, null, 2));

    this.campaign = campaign;
    this.isInitialized = true;

    this.emit('campaignInitialized', campaign);
    return campaign;
  }

  /**
   * Load existing campaign from disk
   */
  async loadCampaign() {
    const campaignFile = path.join(this.campaignPath, 'campaign.json');
    
    try {
      const campaignData = await fs.readFile(campaignFile, 'utf8');
      this.campaign = JSON.parse(campaignData);
      this.isInitialized = true;
      
      // Rebuild indexes from existing data
      await this.rebuildIndexes();
      
      this.emit('campaignLoaded', this.campaign);
      return this.campaign;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error('Campaign not found. Use initializeCampaign() to create a new campaign.');
      }
      throw new Error(`Failed to load campaign: ${error.message}`);
    }
  }

  /**
   * Save an entity to disk and update indexes
   */
  async saveEntity(entityType, entityId, entityData) {
    if (!this.isInitialized) {
      throw new Error('Campaign not initialized. Call initializeCampaign() or loadCampaign() first.');
    }

    if (!this.entityTypes.includes(entityType)) {
      throw new Error(`Invalid entity type: ${entityType}`);
    }

    // Validate entity data
    if (!entityData.id) {
      throw new Error('Entity must have an id field');
    }

    if (entityData.id !== entityId) {
      throw new Error('Entity id must match the provided id');
    }

    // Add metadata
    const now = new Date().toISOString();
    const existingEntity = this.cache.get(`${entityType}:${entityId}`);
    
    const entityToSave = {
      ...entityData,
      created: existingEntity?.created || now,
      lastModified: now
    };

    // Save to file
    const entityDir = path.join(this.campaignPath, entityType);
    const entityFile = path.join(entityDir, `${entityId}.json`);
    await fs.writeFile(entityFile, JSON.stringify(entityToSave, null, 2));

    // Update cache
    this.cache.set(`${entityType}:${entityId}`, entityToSave);

    // Update indexes
    this.updateIndexes(entityType, entityId, entityToSave);

    this.emit('entitySaved', { entityType, entityId, entityData: entityToSave });
    return entityToSave;
  }

  /**
   * Load an entity from cache or disk
   */
  async loadEntity(entityType, entityId) {
    const cacheKey = `${entityType}:${entityId}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Load from disk
    const entityFile = path.join(this.campaignPath, entityType, `${entityId}.json`);
    
    try {
      const entityData = await fs.readFile(entityFile, 'utf8');
      const entity = JSON.parse(entityData);
      
      // Cache the entity
      this.cache.set(cacheKey, entity);
      
      return entity;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`Entity not found: ${entityType}:${entityId}`);
      }
      if (error instanceof SyntaxError) {
        throw new Error(`Failed to parse entity data: ${entityType}:${entityId}`);
      }
      throw error;
    }
  }

  /**
   * Delete an entity from disk and indexes
   */
  async deleteEntity(entityType, entityId) {
    const entityFile = path.join(this.campaignPath, entityType, `${entityId}.json`);
    
    // Remove from file system
    await fs.unlink(entityFile);
    
    // Remove from cache
    const cacheKey = `${entityType}:${entityId}`;
    this.cache.delete(cacheKey);
    
    // Remove from indexes
    this.removeFromIndexes(entityType, entityId);
    
    this.emit('entityDeleted', { entityType, entityId });
  }

  /**
   * Get all entities of a specific type
   */
  async getAllEntities(entityType) {
    const entityDir = path.join(this.campaignPath, entityType);
    
    try {
      const files = await fs.readdir(entityDir);
      const entityFiles = files.filter(file => file.endsWith('.json') && file !== 'index.json');
      
      const entities = [];
      for (const file of entityFiles) {
        const entityId = path.basename(file, '.json');
        try {
          const entity = await this.loadEntity(entityType, entityId);
          entities.push(entity);
        } catch (error) {
          // Skip corrupted entities but log the error
          console.warn(`Skipping corrupted entity: ${entityType}:${entityId}`, error.message);
        }
      }
      
      return entities;
    } catch (error) {
      if (error.code === 'ENOENT') {
        return [];
      }
      throw error;
    }
  }

  /**
   * Find entities by name (exact or fuzzy matching)
   */
  async findEntitiesByName(query, fuzzy = false) {
    const normalizedQuery = query.toLowerCase().trim();
    
    // Check exact matches first
    const exactMatches = this.indexes.byName.get(normalizedQuery) || [];
    
    if (!fuzzy || exactMatches.length > 0) {
      return this.resolveEntityReferences(exactMatches);
    }

    // Fuzzy matching for transcription errors
    const fuzzyMatches = [];
    const threshold = this.campaign?.settings?.fuzzyMatchThreshold || 0.8;
    
    for (const [indexedName, entityRefs] of this.indexes.byName) {
      const similarity = this.calculateStringSimilarity(normalizedQuery, indexedName);
      if (similarity >= threshold) {
        fuzzyMatches.push(...entityRefs);
      }
    }
    
    return this.resolveEntityReferences(fuzzyMatches);
  }

  /**
   * Find entities by location
   */
  async findEntitiesByLocation(locationId) {
    const entityRefs = this.indexes.byLocation.get(locationId) || [];
    return this.resolveEntityReferences(entityRefs);
  }

  /**
   * Find entities by tag
   */
  async findEntitiesByTag(tag) {
    const normalizedTag = tag.toLowerCase().trim();
    const entityRefs = this.indexes.byTag.get(normalizedTag) || [];
    return this.resolveEntityReferences(entityRefs);
  }

  /**
   * Update entity mention count and timeline
   */
  async updateEntityMention(entityType, entityId, context) {
    const entity = await this.loadEntity(entityType, entityId);

    // Update mention tracking
    entity.mentionCount = (entity.mentionCount || 0) + 1;
    entity.lastMentioned = context.timestamp;

    // Add to timeline
    if (!entity.timeline) {
      entity.timeline = [];
    }

    entity.timeline.push({
      timestamp: context.timestamp,
      event: 'mention',
      context: context.context
    });

    // Save updated entity
    await this.saveEntity(entityType, entityId, entity);

    this.emit('entityMentioned', { entityType, entityId, context });
    return entity;
  }

  /**
   * Log a session event
   */
  async logSessionEvent(event) {
    const sessionDate = new Date().toISOString().split('T')[0];
    const sessionFile = path.join(this.campaignPath, 'sessions', `${sessionDate}.json`);

    let sessionData;
    try {
      const existingData = await fs.readFile(sessionFile, 'utf8');
      sessionData = JSON.parse(existingData);
    } catch (error) {
      // Create new session file
      sessionData = {
        date: sessionDate,
        events: []
      };
    }

    // Add timestamp to event
    const eventWithTimestamp = {
      ...event,
      timestamp: new Date().toISOString()
    };

    sessionData.events.push(eventWithTimestamp);

    // Save session file
    await fs.writeFile(sessionFile, JSON.stringify(sessionData, null, 2));

    this.emit('sessionEventLogged', eventWithTimestamp);
    return eventWithTimestamp;
  }

  /**
   * Rebuild all indexes from existing data
   */
  async rebuildIndexes() {
    // Clear existing indexes
    this.indexes.byName.clear();
    this.indexes.byLocation.clear();
    this.indexes.byMentionFrequency.clear();
    this.indexes.byTag.clear();

    // Rebuild from all entity types
    for (const entityType of this.entityTypes) {
      const entities = await this.getAllEntities(entityType);

      for (const entity of entities) {
        this.updateIndexes(entityType, entity.id, entity);
      }
    }

    this.emit('indexesRebuilt');
  }

  /**
   * Update indexes for a specific entity
   */
  updateIndexes(entityType, entityId, entityData) {
    const entityRef = `${entityType}:${entityId}`;

    // Index by name
    if (entityData.name) {
      const normalizedName = entityData.name.toLowerCase().trim();
      this.addToIndex(this.indexes.byName, normalizedName, entityRef);
    }

    // Index by aliases
    if (entityData.aliases && Array.isArray(entityData.aliases)) {
      for (const alias of entityData.aliases) {
        const normalizedAlias = alias.toLowerCase().trim();
        this.addToIndex(this.indexes.byName, normalizedAlias, entityRef);
      }
    }

    // Index by location
    if (entityData.location) {
      this.addToIndex(this.indexes.byLocation, entityData.location, entityRef);
    }

    // Index by tags
    if (entityData.tags && Array.isArray(entityData.tags)) {
      for (const tag of entityData.tags) {
        const normalizedTag = tag.toLowerCase().trim();
        this.addToIndex(this.indexes.byTag, normalizedTag, entityRef);
      }
    }

    // Index by mention frequency
    if (entityData.mentionCount) {
      this.addToIndex(this.indexes.byMentionFrequency, entityData.mentionCount, entityRef);
    }
  }

  /**
   * Remove entity from all indexes
   */
  removeFromIndexes(entityType, entityId) {
    const entityRef = `${entityType}:${entityId}`;

    // Remove from all indexes
    for (const index of Object.values(this.indexes)) {
      for (const [key, refs] of index) {
        const filteredRefs = refs.filter(ref => ref !== entityRef);
        if (filteredRefs.length === 0) {
          index.delete(key);
        } else {
          index.set(key, filteredRefs);
        }
      }
    }
  }

  /**
   * Helper method to add entity reference to an index
   */
  addToIndex(index, key, entityRef) {
    if (!index.has(key)) {
      index.set(key, []);
    }
    const refs = index.get(key);
    if (!refs.includes(entityRef)) {
      refs.push(entityRef);
    }
  }

  /**
   * Resolve entity references to actual entity objects
   */
  async resolveEntityReferences(entityRefs) {
    const entities = [];

    for (const ref of entityRefs) {
      const [entityType, entityId] = ref.split(':');
      try {
        const entity = await this.loadEntity(entityType, entityId);
        entities.push(entity);
      } catch (error) {
        // Skip entities that can't be loaded
        console.warn(`Failed to resolve entity reference: ${ref}`, error.message);
      }
    }

    return entities;
  }

  /**
   * Calculate string similarity for fuzzy matching
   */
  calculateStringSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) {
      return 1.0;
    }

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Create a backup of the current campaign data
   */
  async createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `backup-${timestamp}`;
    const backupPath = path.join(this.campaignPath, 'backups', backupName);

    await fs.mkdir(backupPath, { recursive: true });

    // Copy campaign.json
    const campaignSource = path.join(this.campaignPath, 'campaign.json');
    const campaignDest = path.join(backupPath, 'campaign.json');
    await fs.copyFile(campaignSource, campaignDest);

    // Copy all entity directories
    for (const entityType of this.entityTypes) {
      const sourceDir = path.join(this.campaignPath, entityType);
      const destDir = path.join(backupPath, entityType);

      try {
        await this.copyDirectory(sourceDir, destDir);
      } catch (error) {
        // Skip if directory doesn't exist
        if (error.code !== 'ENOENT') {
          throw error;
        }
      }
    }

    // Copy sessions directory
    const sessionsSource = path.join(this.campaignPath, 'sessions');
    const sessionsDest = path.join(backupPath, 'sessions');
    try {
      await this.copyDirectory(sessionsSource, sessionsDest);
    } catch (error) {
      // Skip if directory doesn't exist
      if (error.code !== 'ENOENT') {
        throw error;
      }
    }

    this.emit('backupCreated', { backupName, backupPath });
    return backupName;
  }

  /**
   * List available backups
   */
  async listBackups() {
    const backupsDir = path.join(this.campaignPath, 'backups');

    try {
      const backups = await fs.readdir(backupsDir);
      return backups.filter(name => name.startsWith('backup-')).sort().reverse();
    } catch (error) {
      if (error.code === 'ENOENT') {
        return [];
      }
      throw error;
    }
  }

  /**
   * Restore campaign data from a backup
   */
  async restoreFromBackup(backupName) {
    const backupPath = path.join(this.campaignPath, 'backups', backupName);

    // Verify backup exists
    try {
      await fs.access(backupPath);
    } catch (error) {
      throw new Error(`Backup not found: ${backupName}`);
    }

    // Clear current cache
    this.cache.clear();

    // Restore campaign.json
    const campaignSource = path.join(backupPath, 'campaign.json');
    const campaignDest = path.join(this.campaignPath, 'campaign.json');
    await fs.copyFile(campaignSource, campaignDest);

    // Restore entity directories
    for (const entityType of this.entityTypes) {
      const sourceDir = path.join(backupPath, entityType);
      const destDir = path.join(this.campaignPath, entityType);

      // Remove existing directory
      try {
        await fs.rm(destDir, { recursive: true, force: true });
      } catch (error) {
        // Ignore if directory doesn't exist
      }

      // Copy from backup
      try {
        await this.copyDirectory(sourceDir, destDir);
      } catch (error) {
        if (error.code !== 'ENOENT') {
          throw error;
        }
      }
    }

    // Restore sessions directory
    const sessionsSource = path.join(backupPath, 'sessions');
    const sessionsDest = path.join(this.campaignPath, 'sessions');

    try {
      await fs.rm(sessionsDest, { recursive: true, force: true });
    } catch (error) {
      // Ignore if directory doesn't exist
    }

    try {
      await this.copyDirectory(sessionsSource, sessionsDest);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        throw error;
      }
    }

    // Reload campaign and rebuild indexes
    await this.loadCampaign();

    this.emit('backupRestored', { backupName });
    return this.campaign;
  }

  /**
   * Helper method to recursively copy a directory
   */
  async copyDirectory(source, destination) {
    await fs.mkdir(destination, { recursive: true });

    const entries = await fs.readdir(source, { withFileTypes: true });

    for (const entry of entries) {
      const sourcePath = path.join(source, entry.name);
      const destPath = path.join(destination, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(sourcePath, destPath);
      } else {
        await fs.copyFile(sourcePath, destPath);
      }
    }
  }

  /**
   * Get campaign statistics
   */
  async getCampaignStats() {
    const stats = {
      campaign: this.campaign,
      entityCounts: {},
      totalEntities: 0,
      cacheSize: this.cache.size,
      indexSizes: {
        byName: this.indexes.byName.size,
        byLocation: this.indexes.byLocation.size,
        byTag: this.indexes.byTag.size,
        byMentionFrequency: this.indexes.byMentionFrequency.size
      }
    };

    for (const entityType of this.entityTypes) {
      const entities = await this.getAllEntities(entityType);
      stats.entityCounts[entityType] = entities.length;
      stats.totalEntities += entities.length;
    }

    return stats;
  }

  /**
   * Clean up resources
   */
  close() {
    this.cache.clear();
    this.indexes.byName.clear();
    this.indexes.byLocation.clear();
    this.indexes.byMentionFrequency.clear();
    this.indexes.byTag.clear();
    this.removeAllListeners();
  }
}
