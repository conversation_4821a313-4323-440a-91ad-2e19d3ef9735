/**
 * Agent<PERSON>anager - Manages agent lifecycle and integrates with file tailing
 * Coordinates content processing through registered agents
 * Handles error isolation and event broadcasting
 */

import { EventEmitter } from 'events';

/**
 * Manages the lifecycle and execution of agents in the lorecat system
 * Acts as the coordinator between file tailing and agent processing
 */
export class AgentManager extends EventEmitter {
  constructor(dataManager, broadcastFunction) {
    super();
    this.dataManager = dataManager;        // CampaignDataManager instance
    this.broadcastToClients = broadcastFunction; // WebSocket broadcast function
    this.agents = new Map();               // Registered agents (name -> agent)
    this.isRunning = false;                // System running state
  }

  /**
   * Start the agent system
   */
  async startSystem() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.emit('systemStarted');
    console.log('🚀 Agent system started');
  }

  /**
   * Stop the agent system and cleanup all agents
   */
  async stopSystem() {
    if (!this.isRunning) {
      return;
    }

    console.log('🛑 Stopping agent system...');
    
    // Gracefully shut down all agents
    for (const [agent<PERSON><PERSON>, agent] of this.agents) {
      try {
        await agent.cleanup();
        console.log(`✅ Agent stopped: ${agentName}`);
      } catch (error) {
        console.error(`❌ Error stopping agent ${agentName}:`, error.message);
        // Continue with other agents even if one fails
      }
    }
    
    this.isRunning = false;
    this.emit('systemStopped');
    console.log('✅ Agent system stopped');
  }

  /**
   * Register a new agent with the system
   * @param {Class} AgentClass - The agent class to instantiate
   * @param {Object} config - Configuration for the agent
   * @returns {Object} - The created and initialized agent instance
   */
  async registerAgent(AgentClass, config = {}) {
    const agentName = AgentClass.name;
    
    // Check for duplicate registration
    if (this.agents.has(agentName)) {
      throw new Error(`Agent ${agentName} is already registered`);
    }

    console.log(`📝 Registering agent: ${agentName}`);

    try {
      // 1. Create agent instance with dependency injection
      const agent = new AgentClass(this.dataManager);
      
      // 2. Apply configuration
      if (config.settings) {
        agent.applyConfig(config);
      }
      
      // 3. Set up event forwarding for agent events
      agent.on('agentEvent', (event) => {
        this.broadcastToClients({
          type: 'agentEvent',
          agentType: agentName,
          eventType: event.eventType,
          data: event.data,
          timestamp: event.timestamp
        });
      });

      // 4. Set up error handling
      agent.on('agentError', (error) => {
        this.broadcastToClients({
          type: 'agentError',
          agentType: agentName,
          error: error.error,
          operation: error.operation,
          timestamp: error.timestamp
        });
      });
      
      // 5. Initialize agent
      await agent.initialize();
      
      // 6. Store agent
      this.agents.set(agentName, agent);
      
      console.log(`✅ Agent registered: ${agentName}`);
      return agent;
      
    } catch (error) {
      console.error(`❌ Failed to register agent ${agentName}:`, error.message);
      throw error;
    }
  }

  /**
   * Unregister an agent from the system
   * @param {string} agentName - Name of the agent to unregister
   */
  async unregisterAgent(agentName) {
    const agent = this.agents.get(agentName);
    
    if (!agent) {
      throw new Error(`Agent ${agentName} not found`);
    }

    console.log(`🗑️ Unregistering agent: ${agentName}`);

    try {
      // Cleanup agent
      await agent.cleanup();
      
      // Remove from registry
      this.agents.delete(agentName);
      
      console.log(`✅ Agent unregistered: ${agentName}`);
    } catch (error) {
      console.error(`❌ Error unregistering agent ${agentName}:`, error.message);
      // Remove from registry even if cleanup failed
      this.agents.delete(agentName);
      throw error;
    }
  }

  /**
   * Process content through all registered agents
   * @param {string} content - The content to process
   * @param {Object} metadata - Optional metadata about the content
   */
  async processContent(content, metadata = {}) {
    // Skip processing if system is not running or content is empty
    if (!this.isRunning || !content || typeof content !== 'string' || !content.trim()) {
      return;
    }

    const agentCount = this.agents.size;
    if (agentCount === 0) {
      return;
    }

    console.log(`🎯 Processing content through ${agentCount} agents`);
    
    // Process content through all agents in parallel
    const processingPromises = [];
    
    for (const [agentName, agent] of this.agents) {
      // Check if agent can process this content
      if (agent.canProcess && !agent.canProcess(content)) {
        continue; // Skip this agent
      }
      
      // Process content (don't await - run in parallel)
      const promise = this.processWithAgent(agent, content, metadata)
        .catch(error => {
          console.error(`❌ Agent ${agentName} failed:`, error.message);
          
          // Emit error event but don't crash other agents
          this.broadcastToClients({
            type: 'agentError',
            agentType: agentName,
            error: error.message,
            timestamp: new Date().toISOString()
          });
        });
      
      processingPromises.push(promise);
    }
    
    // Wait for all agents to complete (or fail)
    await Promise.allSettled(processingPromises);
  }

  /**
   * Process content with a specific agent
   * @param {Object} agent - The agent to process with
   * @param {string} content - The content to process
   * @param {Object} metadata - Metadata about the content
   */
  async processWithAgent(agent, content, metadata) {
    const startTime = Date.now();
    
    try {
      // Call the agent's process method
      await agent.process(content, metadata);
      
      const processingTime = Date.now() - startTime;
      
      // Performance monitoring
      if (processingTime > 100) {
        console.warn(`⚠️ Agent ${agent.constructor.name} took ${processingTime}ms`);
      }
      
    } catch (error) {
      // Re-throw to be caught by processContent
      throw new Error(`${agent.constructor.name}: ${error.message}`);
    }
  }

  /**
   * Get status information for a specific agent
   * @param {string} agentName - Name of the agent
   * @returns {Object} - Agent status information
   */
  getAgentStatus(agentName) {
    const agent = this.agents.get(agentName);
    
    if (!agent) {
      throw new Error(`Agent ${agentName} not found`);
    }
    
    return agent.getStatus();
  }

  /**
   * Get overall system status information
   * @returns {Object} - System status information
   */
  getSystemStatus() {
    return {
      isRunning: this.isRunning,
      agentCount: this.agents.size,
      agents: Array.from(this.agents.keys())
    };
  }

  /**
   * Get all registered agents
   * @returns {Map} - Map of agent names to agent instances
   */
  getAgents() {
    return new Map(this.agents);
  }

  /**
   * Check if an agent is registered
   * @param {string} agentName - Name of the agent to check
   * @returns {boolean} - True if agent is registered
   */
  hasAgent(agentName) {
    return this.agents.has(agentName);
  }

  /**
   * Get performance metrics for the agent system
   * @returns {Object} - Performance metrics
   */
  getPerformanceMetrics() {
    const metrics = {
      totalAgents: this.agents.size,
      runningAgents: 0,
      initializedAgents: 0,
      agentDetails: {}
    };

    for (const [agentName, agent] of this.agents) {
      const status = agent.getStatus();
      
      if (status.isInitialized) {
        metrics.initializedAgents++;
        metrics.runningAgents++;
      }
      
      metrics.agentDetails[agentName] = {
        isInitialized: status.isInitialized,
        listenerCount: status.listenerCount,
        settings: status.settings
      };
    }

    return metrics;
  }

  /**
   * Restart a specific agent
   * @param {string} agentName - Name of the agent to restart
   */
  async restartAgent(agentName) {
    const agent = this.agents.get(agentName);
    
    if (!agent) {
      throw new Error(`Agent ${agentName} not found`);
    }

    console.log(`🔄 Restarting agent: ${agentName}`);

    try {
      // Cleanup current agent
      await agent.cleanup();
      
      // Re-initialize
      await agent.initialize();
      
      console.log(`✅ Agent restarted: ${agentName}`);
    } catch (error) {
      console.error(`❌ Failed to restart agent ${agentName}:`, error.message);
      throw error;
    }
  }

  /**
   * Clean up the agent manager and all agents
   */
  async cleanup() {
    await this.stopSystem();
    this.agents.clear();
    this.removeAllListeners();
  }
}
