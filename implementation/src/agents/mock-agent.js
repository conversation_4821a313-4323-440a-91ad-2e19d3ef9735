/**
 * MockAgent - Simple test agent for validating the agent system
 * Used for testing and demonstration purposes
 */

import { BaseAgent } from './base-agent.js';

/**
 * Simple mock agent that processes content containing specific keywords
 * Useful for testing the agent system without complex domain logic
 */
export class MockAgent extends BaseAgent {
  constructor(dataManager) {
    super();
    this.dataManager = dataManager;
    this.processedContent = [];
    this.keywords = ['test', 'mock', 'demo'];
  }

  /**
   * Initialize the mock agent
   */
  async initialize() {
    this.debug('Initializing MockAgent');
    
    // Apply default settings if not configured
    if (!this.settings.keywords) {
      this.settings.keywords = this.keywords;
    }
    
    this.isInitialized = true;
    this.emitAgentEvent('agentReady', { 
      message: 'MockAgent initialized successfully',
      keywords: this.settings.keywords
    });
    
    this.debug('MockAgent initialized', { keywords: this.settings.keywords });
  }

  /**
   * Process content by looking for configured keywords
   * @param {string} content - Content to process
   * @param {Object} metadata - Content metadata
   */
  async process(content, metadata = {}) {
    this.requireInitialization();
    
    const timer = this.createTimer('MockAgent.process');
    
    try {
      // Find keywords in content
      const foundKeywords = this.settings.keywords.filter(keyword => 
        content.toLowerCase().includes(keyword.toLowerCase())
      );
      
      if (foundKeywords.length > 0) {
        const processedItem = {
          content,
          metadata,
          foundKeywords,
          timestamp: new Date().toISOString()
        };
        
        this.processedContent.push(processedItem);
        
        // Emit event for each found keyword
        foundKeywords.forEach(keyword => {
          this.emitAgentEvent('keywordFound', {
            keyword,
            content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
            metadata
          });
        });
        
        this.debug(`Processed content with keywords: ${foundKeywords.join(', ')}`);
      }
      
    } finally {
      timer.stop();
    }
  }

  /**
   * Check if this agent can process the given content
   * @param {string} content - Content to check
   * @returns {boolean} - True if content contains any configured keywords
   */
  canProcess(content) {
    if (!content || typeof content !== 'string') {
      return false;
    }
    
    return this.settings.keywords.some(keyword => 
      content.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * Get processing statistics
   * @returns {Object} - Processing statistics
   */
  getStats() {
    const keywordCounts = {};
    
    this.processedContent.forEach(item => {
      item.foundKeywords.forEach(keyword => {
        keywordCounts[keyword] = (keywordCounts[keyword] || 0) + 1;
      });
    });
    
    return {
      totalProcessed: this.processedContent.length,
      keywordCounts,
      lastProcessed: this.processedContent.length > 0 
        ? this.processedContent[this.processedContent.length - 1].timestamp
        : null
    };
  }

  /**
   * Clear processed content history
   */
  clearHistory() {
    this.processedContent = [];
    this.debug('Cleared processing history');
  }

  /**
   * Get recent processed content
   * @param {number} limit - Maximum number of items to return
   * @returns {Array} - Recent processed content
   */
  getRecentContent(limit = 10) {
    return this.processedContent.slice(-limit);
  }

  /**
   * Override cleanup to clear history
   */
  cleanup() {
    this.clearHistory();
    super.cleanup();
    this.debug('MockAgent cleaned up');
  }

  /**
   * Override getStatus to include mock-specific information
   * @returns {Object} - Extended status information
   */
  getStatus() {
    const baseStatus = super.getStatus();
    const stats = this.getStats();
    
    return {
      ...baseStatus,
      stats,
      keywords: this.settings.keywords || []
    };
  }
}
