/**
 * BaseAgent - Abstract base class for all agents
 * Provides standardized interface and common functionality
 * Following Agent API Conventions specification
 */

import { EventEmitter } from 'events';

/**
 * Abstract base class for all agents in the lorecat system
 * Provides common functionality and enforces agent interface
 */
export class BaseAgent extends EventEmitter {
  constructor() {
    super();
    this.isInitialized = false;
    this.settings = {};
  }

  /**
   * Initialize the agent - MUST be implemented by subclasses
   * Should load domain-specific data, build caches, and subscribe to events
   * Must emit 'agentReady' when initialization is complete
   */
  async initialize() {
    throw new Error('initialize() must be implemented by subclass');
  }

  /**
   * Process content - MUST be implemented by subclasses
   * @param {string} content - The content to process
   * @param {Object} metadata - Optional metadata about the content
   */
  async process(content, metadata = {}) {
    throw new Error('process() must be implemented by subclass');
  }

  /**
   * Check if agent can process the given content
   * Override in subclasses for content filtering
   * @param {string} content - The content to check
   * @returns {boolean} - True if agent can process this content
   */
  canProcess(content) {
    return true;
  }

  /**
   * Clean up agent resources
   * Removes all event listeners and clears caches
   */
  cleanup() {
    this.removeAllListeners();
  }

  /**
   * Safely execute a data operation with error handling
   * Emits 'agentError' event on failure and returns fallback value
   * @param {Function} operation - The operation to execute
   * @param {*} fallback - Value to return on error (default: null)
   * @returns {*} - Operation result or fallback value
   */
  async safeDataOperation(operation, fallback = null) {
    try {
      return await operation();
    } catch (error) {
      this.emit('agentError', {
        agentType: this.constructor.name,
        operation: operation.name || 'unknown',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      return fallback;
    }
  }

  /**
   * Safely load an entity with error handling
   * Convenience method for common data manager operations
   * @param {Object} dataManager - The campaign data manager
   * @param {string} entityType - Type of entity to load
   * @param {string} entityId - ID of entity to load
   * @param {*} fallback - Value to return on error (default: null)
   * @returns {Object|*} - Entity object or fallback value
   */
  async safeEntityLoad(dataManager, entityType, entityId, fallback = null) {
    return this.safeDataOperation(
      () => dataManager.loadEntity(entityType, entityId),
      fallback
    );
  }

  /**
   * Safely save an entity with error handling
   * Convenience method for common data manager operations
   * @param {Object} dataManager - The campaign data manager
   * @param {string} entityType - Type of entity to save
   * @param {string} entityId - ID of entity to save
   * @param {Object} entityData - Entity data to save
   * @returns {Object|null} - Saved entity or null on error
   */
  async safeEntitySave(dataManager, entityType, entityId, entityData) {
    return this.safeDataOperation(
      () => dataManager.saveEntity(entityType, entityId, entityData),
      null
    );
  }

  /**
   * Safely search for entities with error handling
   * Convenience method for common data manager operations
   * @param {Object} dataManager - The campaign data manager
   * @param {string} query - Search query
   * @param {boolean} fuzzy - Whether to use fuzzy matching
   * @returns {Array} - Array of found entities or empty array on error
   */
  async safeEntitySearch(dataManager, query, fuzzy = false) {
    return this.safeDataOperation(
      () => dataManager.findEntitiesByName(query, fuzzy),
      []
    );
  }

  /**
   * Emit a standardized agent event
   * @param {string} eventType - Type of event (e.g., 'npcMentioned', 'locationChange')
   * @param {Object} data - Event data
   */
  emitAgentEvent(eventType, data) {
    this.emit('agentEvent', {
      agentType: this.constructor.name,
      eventType: eventType,
      data: data,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log a debug message with agent context
   * @param {string} message - Debug message
   * @param {Object} context - Additional context data
   */
  debug(message, context = {}) {
    console.log(`[${this.constructor.name}] ${message}`, context);
  }

  /**
   * Log a warning message with agent context
   * @param {string} message - Warning message
   * @param {Object} context - Additional context data
   */
  warn(message, context = {}) {
    console.warn(`[${this.constructor.name}] ${message}`, context);
  }

  /**
   * Log an error message with agent context
   * @param {string} message - Error message
   * @param {Error|Object} error - Error object or context data
   */
  error(message, error = {}) {
    console.error(`[${this.constructor.name}] ${message}`, error);
  }

  /**
   * Get agent status information
   * @returns {Object} - Agent status object
   */
  getStatus() {
    return {
      agentType: this.constructor.name,
      isInitialized: this.isInitialized,
      settings: { ...this.settings },
      listenerCount: this.eventNames().reduce((count, eventName) => {
        return count + this.listenerCount(eventName);
      }, 0)
    };
  }

  /**
   * Validate that the agent is initialized before processing
   * Throws error if not initialized
   */
  requireInitialization() {
    if (!this.isInitialized) {
      throw new Error(`${this.constructor.name} not initialized. Call initialize() first.`);
    }
  }

  /**
   * Apply configuration settings to the agent
   * @param {Object} config - Configuration object
   */
  applyConfig(config) {
    if (config.settings) {
      Object.assign(this.settings, config.settings);
    }
  }

  /**
   * Create a performance timer for measuring operation duration
   * @param {string} operationName - Name of the operation being timed
   * @returns {Object} - Timer object with stop() method
   */
  createTimer(operationName) {
    const startTime = process.hrtime.bigint();
    
    return {
      stop: () => {
        const endTime = process.hrtime.bigint();
        const durationMs = Number(endTime - startTime) / 1000000;
        
        if (durationMs > 100) {
          this.warn(`Slow operation: ${operationName} took ${durationMs.toFixed(2)}ms`);
        }
        
        return durationMs;
      }
    };
  }

  /**
   * Throttle function calls to prevent excessive processing
   * @param {Function} func - Function to throttle
   * @param {number} delay - Delay in milliseconds
   * @returns {Function} - Throttled function
   */
  throttle(func, delay) {
    let lastCall = 0;
    
    return (...args) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        return func.apply(this, args);
      }
    };
  }

  /**
   * Debounce function calls to prevent excessive processing
   * @param {Function} func - Function to debounce
   * @param {number} delay - Delay in milliseconds
   * @returns {Function} - Debounced function
   */
  debounce(func, delay) {
    let timeoutId;
    
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }
}
