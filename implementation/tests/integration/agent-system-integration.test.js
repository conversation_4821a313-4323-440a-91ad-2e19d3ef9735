/**
 * Integration tests for the complete agent system
 * Tests the interaction between AgentManager, BaseAgent, and MockAgent
 */

import { jest } from '@jest/globals';
import { AgentManager } from '../../src/agent-manager.js';
import { MockAgent } from '../../src/agents/mock-agent.js';

describe('Agent System Integration', () => {
  let agentManager;
  let mockDataManager;
  let mockBroadcastFunction;
  let broadcastedEvents;

  beforeEach(() => {
    // Mock data manager
    mockDataManager = {
      loadEntity: jest.fn().mockResolvedValue({}),
      saveEntity: jest.fn().mockResolvedValue({}),
      findEntitiesByName: jest.fn().mockResolvedValue([])
    };

    // Mock broadcast function that captures events
    broadcastedEvents = [];
    mockBroadcastFunction = jest.fn((event) => {
      broadcastedEvents.push(event);
    });
    
    agentManager = new AgentManager(mockDataManager, mockBroadcastFunction);
  });

  afterEach(async () => {
    if (agentManager) {
      await agentManager.cleanup();
    }
  });

  describe('Complete System Workflow', () => {
    test('should register agent, start system, and process content end-to-end', async () => {
      // 1. Register MockAgent with custom configuration
      const agent = await agentManager.registerAgent(MockAgent, {
        settings: {
          keywords: ['gandalf', 'frodo', 'ring']
        }
      });

      expect(agent).toBeInstanceOf(MockAgent);
      expect(agent.isInitialized).toBe(true);
      expect(agent.settings.keywords).toEqual(['gandalf', 'frodo', 'ring']);

      // 2. Start the system
      await agentManager.startSystem();
      expect(agentManager.isRunning).toBe(true);

      // 3. Process content that should trigger the agent
      const content = 'Gandalf the Grey arrived at Bag End to visit Frodo about the ring.';
      const metadata = { 
        filePath: 'test.log',
        timestamp: '2024-08-19T19:15:00Z',
        position: 100,
        size: 200
      };

      await agentManager.processContent(content, metadata);

      // 4. Verify agent processed the content
      expect(agent.processedContent).toHaveLength(1);
      expect(agent.processedContent[0]).toEqual({
        content,
        metadata,
        foundKeywords: ['gandalf', 'frodo', 'ring'],
        timestamp: expect.any(String)
      });

      // 5. Verify events were broadcast
      const keywordEvents = broadcastedEvents.filter(e => e.eventType === 'keywordFound');
      expect(keywordEvents).toHaveLength(3); // gandalf, frodo, ring

      expect(keywordEvents[0]).toEqual({
        type: 'agentEvent',
        agentType: 'MockAgent',
        eventType: 'keywordFound',
        data: {
          keyword: 'gandalf',
          content: content,
          metadata
        },
        timestamp: expect.any(String)
      });
    });

    test('should handle multiple agents processing same content', async () => {
      // Register two agents with different keywords
      const agent1 = await agentManager.registerAgent(MockAgent, {
        settings: { keywords: ['test', 'demo'] }
      });

      // Create a second mock agent class to avoid duplicate registration
      class SecondMockAgent extends MockAgent {
        constructor(dataManager) {
          super(dataManager);
          this.settings.keywords = ['example', 'sample'];
        }
      }

      const agent2 = await agentManager.registerAgent(SecondMockAgent, {
        settings: { keywords: ['example', 'sample'] }
      });

      await agentManager.startSystem();

      // Process content that triggers both agents
      const content = 'This is a test example for the demo sample.';
      await agentManager.processContent(content);

      // Both agents should have processed the content
      expect(agent1.processedContent).toHaveLength(1);
      expect(agent1.processedContent[0].foundKeywords).toEqual(['test', 'demo']);

      expect(agent2.processedContent).toHaveLength(1);
      expect(agent2.processedContent[0].foundKeywords).toEqual(['example', 'sample']);

      // Should have 4 keyword events total
      const keywordEvents = broadcastedEvents.filter(e => e.eventType === 'keywordFound');
      expect(keywordEvents).toHaveLength(4);
    });

    test('should handle agent errors gracefully without affecting other agents', async () => {
      // Create a failing agent
      class FailingAgent extends MockAgent {
        async process() {
          throw new Error('Simulated processing failure');
        }
      }

      const workingAgent = await agentManager.registerAgent(MockAgent, {
        settings: { keywords: ['test'] }
      });

      const failingAgent = await agentManager.registerAgent(FailingAgent, {
        settings: { keywords: ['test'] }
      });

      await agentManager.startSystem();

      // Process content that both agents can handle
      await agentManager.processContent('This is a test message.');

      // Working agent should still process successfully
      expect(workingAgent.processedContent).toHaveLength(1);

      // Error should be broadcast
      const errorEvents = broadcastedEvents.filter(e => e.type === 'agentError');
      expect(errorEvents).toHaveLength(1);
      expect(errorEvents[0]).toEqual({
        type: 'agentError',
        agentType: 'FailingAgent',
        error: 'FailingAgent: Simulated processing failure',
        timestamp: expect.any(String)
      });
    });

    test('should respect agent canProcess filtering', async () => {
      const agent = await agentManager.registerAgent(MockAgent, {
        settings: { keywords: ['specific'] }
      });

      await agentManager.startSystem();

      // Process content that doesn't match agent keywords
      await agentManager.processContent('This content has no matching keywords.');

      // Agent should not have processed anything
      expect(agent.processedContent).toHaveLength(0);

      // No keyword events should be broadcast
      const keywordEvents = broadcastedEvents.filter(e => e.eventType === 'keywordFound');
      expect(keywordEvents).toHaveLength(0);

      // Now process content that does match
      await agentManager.processContent('This content has a specific keyword.');

      // Agent should have processed this content
      expect(agent.processedContent).toHaveLength(1);
      expect(agent.processedContent[0].foundKeywords).toEqual(['specific']);
    });

    test('should provide comprehensive system status', async () => {
      const agent1 = await agentManager.registerAgent(MockAgent, {
        settings: { keywords: ['test'] }
      });

      class SecondMockAgent extends MockAgent {}
      const agent2 = await agentManager.registerAgent(SecondMockAgent, {
        settings: { keywords: ['demo'] }
      });

      await agentManager.startSystem();

      // Process some content
      await agentManager.processContent('This is a test and demo content.');

      // Check system status
      const systemStatus = agentManager.getSystemStatus();
      expect(systemStatus).toEqual({
        isRunning: true,
        agentCount: 2,
        agents: ['MockAgent', 'SecondMockAgent']
      });

      // Check individual agent status
      const agent1Status = agentManager.getAgentStatus('MockAgent');
      expect(agent1Status).toEqual({
        agentType: 'MockAgent',
        isInitialized: true,
        settings: { keywords: ['test'] },
        listenerCount: expect.any(Number),
        stats: {
          totalProcessed: 1,
          keywordCounts: { test: 1 },
          lastProcessed: expect.any(String)
        },
        keywords: ['test']
      });

      // Check performance metrics
      const metrics = agentManager.getPerformanceMetrics();
      expect(metrics).toEqual({
        totalAgents: 2,
        runningAgents: 2,
        initializedAgents: 2,
        agentDetails: {
          MockAgent: {
            isInitialized: true,
            listenerCount: expect.any(Number),
            settings: { keywords: ['test'] }
          },
          SecondMockAgent: {
            isInitialized: true,
            listenerCount: expect.any(Number),
            settings: { keywords: ['demo'] }
          }
        }
      });
    });

    test('should handle system lifecycle correctly', async () => {
      const systemStartedListener = jest.fn();
      const systemStoppedListener = jest.fn();

      agentManager.on('systemStarted', systemStartedListener);
      agentManager.on('systemStopped', systemStoppedListener);

      const agent = await agentManager.registerAgent(MockAgent);

      // Start system
      await agentManager.startSystem();
      expect(systemStartedListener).toHaveBeenCalled();
      expect(agentManager.isRunning).toBe(true);

      // Process content
      await agentManager.processContent('This is a test message.');
      expect(agent.processedContent).toHaveLength(1);

      // Store the count before stopping
      const processedCountBeforeStop = agent.processedContent.length;

      // Stop system
      await agentManager.stopSystem();
      expect(systemStoppedListener).toHaveBeenCalled();
      expect(agentManager.isRunning).toBe(false);

      // Content should not be processed when stopped
      await agentManager.processContent('This is another test message.');

      // The agent's processedContent might be cleared during cleanup,
      // so we just verify the system doesn't process when stopped
      expect(agentManager.isRunning).toBe(false);
    });

    test('should handle agent unregistration', async () => {
      const agent = await agentManager.registerAgent(MockAgent);
      const cleanupSpy = jest.spyOn(agent, 'cleanup');

      expect(agentManager.hasAgent('MockAgent')).toBe(true);

      await agentManager.unregisterAgent('MockAgent');

      expect(agentManager.hasAgent('MockAgent')).toBe(false);
      expect(cleanupSpy).toHaveBeenCalled();
      expect(agentManager.getSystemStatus().agentCount).toBe(0);
    });
  });

  describe('Performance and Reliability', () => {
    test('should handle rapid content processing', async () => {
      const agent = await agentManager.registerAgent(MockAgent, {
        settings: { keywords: ['test'] }
      });

      await agentManager.startSystem();

      // Process multiple content items rapidly
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(agentManager.processContent(`Test message ${i}`));
      }

      await Promise.all(promises);

      // All content should be processed
      expect(agent.processedContent).toHaveLength(10);

      // All keyword events should be broadcast
      const keywordEvents = broadcastedEvents.filter(e => e.eventType === 'keywordFound');
      expect(keywordEvents).toHaveLength(10);
    });

    test('should handle empty and invalid content gracefully', async () => {
      const agent = await agentManager.registerAgent(MockAgent);
      await agentManager.startSystem();

      // These should not cause errors
      await agentManager.processContent('');
      await agentManager.processContent(null);
      await agentManager.processContent(undefined);
      await agentManager.processContent('   ');

      expect(agent.processedContent).toHaveLength(0);
    });
  });
});
