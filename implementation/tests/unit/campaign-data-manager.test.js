/**
 * Test suite for CampaignDataManager
 * Following TDD approach - comprehensive tests before implementation
 */

import { jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { CampaignDataManager } from '../../src/campaign-data-manager.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

describe('CampaignDataManager', () => {
  let tempDir;
  let campaignPath;
  let dataManager;

  beforeEach(async () => {
    // Create temporary directory for each test
    tempDir = path.join(__dirname, '..', 'temp', `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
    campaignPath = path.join(tempDir, 'test-campaign');
    await fs.mkdir(tempDir, { recursive: true });
    
    dataManager = new CampaignDataManager(campaignPath);
  });

  afterEach(async () => {
    // Clean up temporary directory
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Constructor and Initialization', () => {
    test('should create instance with correct properties', () => {
      expect(dataManager.campaignPath).toBe(campaignPath);
      expect(dataManager.cache).toBeInstanceOf(Map);
      expect(dataManager.indexes).toHaveProperty('byName');
      expect(dataManager.indexes).toHaveProperty('byLocation');
      expect(dataManager.indexes).toHaveProperty('byMentionFrequency');
      expect(dataManager.indexes).toHaveProperty('byTag');
      expect(dataManager.campaign).toBeNull();
    });

    test('should initialize new campaign with default structure', async () => {
      const campaignData = {
        name: 'Test Campaign',
        transcriptionFile: '/path/to/test.log'
      };

      await dataManager.initializeCampaign(campaignData);

      // Check campaign.json was created
      const campaignFile = path.join(campaignPath, 'campaign.json');
      expect(await fs.access(campaignFile)).toBeUndefined();

      // Check entity type directories were created
      const entityDirs = ['npcs', 'locations', 'items', 'quests', 'factions'];
      for (const dir of entityDirs) {
        const dirPath = path.join(campaignPath, dir);
        expect(await fs.access(dirPath)).toBeUndefined();

        // Check index.json was created
        const indexPath = path.join(dirPath, 'index.json');
        expect(await fs.access(indexPath)).toBeUndefined();
      }

      // Check sessions directory was created (no index.json for sessions)
      const sessionsDir = path.join(campaignPath, 'sessions');
      expect(await fs.access(sessionsDir)).toBeUndefined();

      // Check backups directory was created
      const backupsDir = path.join(campaignPath, 'backups');
      expect(await fs.access(backupsDir)).toBeUndefined();
    });

    test('should load existing campaign', async () => {
      // Create existing campaign structure
      await fs.mkdir(campaignPath, { recursive: true });
      const campaignData = {
        id: 'test-campaign-123',
        name: 'Existing Campaign',
        created: '2024-01-01T00:00:00Z',
        settings: {
          transcriptionFile: '/path/to/existing.log',
          autoSave: true
        }
      };
      
      await fs.writeFile(
        path.join(campaignPath, 'campaign.json'),
        JSON.stringify(campaignData, null, 2)
      );

      await dataManager.loadCampaign();

      expect(dataManager.campaign).toEqual(campaignData);
    });

    test('should throw error for invalid campaign path', async () => {
      const invalidManager = new CampaignDataManager('');
      await expect(invalidManager.initializeCampaign({ name: 'Test' }))
        .rejects.toThrow('Invalid campaign path');
    });
  });

  describe('Entity CRUD Operations', () => {
    beforeEach(async () => {
      await dataManager.initializeCampaign({
        name: 'Test Campaign',
        transcriptionFile: '/path/to/test.log'
      });
    });

    describe('saveEntity', () => {
      test('should save NPC entity with correct structure', async () => {
        const npcData = {
          id: 'test-npc-1',
          name: 'Gandalf',
          aliases: ['Gandalf the Grey', 'Mithrandir'],
          location: 'rivendell',
          status: 'alive',
          relationship: 'ally',
          importance: 'major',
          notes: 'Wise wizard who guides the fellowship',
          tags: ['wizard', 'important', 'ally']
        };

        await dataManager.saveEntity('npcs', 'test-npc-1', npcData);

        // Verify file was created
        const filePath = path.join(campaignPath, 'npcs', 'test-npc-1.json');
        const savedData = JSON.parse(await fs.readFile(filePath, 'utf8'));
        
        expect(savedData).toMatchObject(npcData);
        expect(savedData.created).toBeDefined();
        expect(savedData.lastModified).toBeDefined();
      });

      test('should update index when saving entity', async () => {
        const npcData = {
          id: 'test-npc-2',
          name: 'Aragorn',
          aliases: ['Strider', 'King Elessar']
        };

        await dataManager.saveEntity('npcs', 'test-npc-2', npcData);

        // Check that entity is in cache
        expect(dataManager.cache.has('npcs:test-npc-2')).toBe(true);
        
        // Check that indexes were updated
        expect(dataManager.indexes.byName.has('aragorn')).toBe(true);
        expect(dataManager.indexes.byName.has('strider')).toBe(true);
        expect(dataManager.indexes.byName.has('king elessar')).toBe(true);
      });

      test('should handle entity updates correctly', async () => {
        const originalData = {
          id: 'test-npc-3',
          name: 'Legolas',
          mentionCount: 5
        };

        await dataManager.saveEntity('npcs', 'test-npc-3', originalData);

        // Wait a small amount to ensure different timestamps
        await new Promise(resolve => setTimeout(resolve, 10));

        const updatedData = {
          ...originalData,
          mentionCount: 10,
          notes: 'Skilled archer from Mirkwood'
        };

        await dataManager.saveEntity('npcs', 'test-npc-3', updatedData);

        const loaded = await dataManager.loadEntity('npcs', 'test-npc-3');
        expect(loaded.mentionCount).toBe(10);
        expect(loaded.notes).toBe('Skilled archer from Mirkwood');
        expect(loaded.lastModified).not.toBe(loaded.created);
      });
    });

    describe('loadEntity', () => {
      test('should load entity from file', async () => {
        const entityData = {
          id: 'test-location-1',
          name: 'Rivendell',
          type: 'settlement',
          description: 'Hidden elven refuge'
        };

        await dataManager.saveEntity('locations', 'test-location-1', entityData);
        
        // Clear cache to force file read
        dataManager.cache.clear();
        
        const loaded = await dataManager.loadEntity('locations', 'test-location-1');
        expect(loaded).toMatchObject(entityData);
      });

      test('should return cached entity when available', async () => {
        const entityData = { id: 'cached-entity', name: 'Cached' };
        dataManager.cache.set('npcs:cached-entity', entityData);

        const loaded = await dataManager.loadEntity('npcs', 'cached-entity');
        expect(loaded).toBe(entityData); // Same reference, not copy
      });

      test('should throw error for non-existent entity', async () => {
        await expect(dataManager.loadEntity('npcs', 'non-existent'))
          .rejects.toThrow('Entity not found');
      });
    });

    describe('deleteEntity', () => {
      test('should delete entity file and update indexes', async () => {
        const entityData = {
          id: 'to-delete',
          name: 'Temporary NPC',
          aliases: ['Temp']
        };

        await dataManager.saveEntity('npcs', 'to-delete', entityData);
        
        // Verify it exists
        expect(dataManager.cache.has('npcs:to-delete')).toBe(true);
        expect(dataManager.indexes.byName.has('temporary npc')).toBe(true);

        await dataManager.deleteEntity('npcs', 'to-delete');

        // Verify it's gone
        expect(dataManager.cache.has('npcs:to-delete')).toBe(false);
        expect(dataManager.indexes.byName.has('temporary npc')).toBe(false);
        
        const filePath = path.join(campaignPath, 'npcs', 'to-delete.json');
        await expect(fs.access(filePath)).rejects.toThrow();
      });
    });

    describe('getAllEntities', () => {
      test('should return all entities of specified type', async () => {
        const entities = [
          { id: 'npc1', name: 'NPC One' },
          { id: 'npc2', name: 'NPC Two' },
          { id: 'npc3', name: 'NPC Three' }
        ];

        for (const entity of entities) {
          await dataManager.saveEntity('npcs', entity.id, entity);
        }

        const allNPCs = await dataManager.getAllEntities('npcs');
        expect(allNPCs).toHaveLength(3);
        expect(allNPCs.map(npc => npc.name)).toEqual(['NPC One', 'NPC Two', 'NPC Three']);
      });

      test('should return empty array for type with no entities', async () => {
        const result = await dataManager.getAllEntities('items');
        expect(result).toEqual([]);
      });
    });
  });

  describe('Search and Query Operations', () => {
    beforeEach(async () => {
      await dataManager.initializeCampaign({
        name: 'Search Test Campaign',
        transcriptionFile: '/path/to/test.log'
      });

      // Add test data
      const testEntities = [
        {
          id: 'gandalf',
          name: 'Gandalf',
          aliases: ['Gandalf the Grey', 'Mithrandir', 'The Grey Pilgrim'],
          location: 'rivendell',
          tags: ['wizard', 'important']
        },
        {
          id: 'aragorn',
          name: 'Aragorn',
          aliases: ['Strider', 'King Elessar'],
          location: 'minas-tirith',
          tags: ['ranger', 'king', 'important']
        },
        {
          id: 'legolas',
          name: 'Legolas',
          aliases: ['Prince of Mirkwood'],
          location: 'mirkwood',
          tags: ['elf', 'archer']
        }
      ];

      for (const entity of testEntities) {
        await dataManager.saveEntity('npcs', entity.id, entity);
      }
    });

    describe('findEntitiesByName', () => {
      test('should find entities by exact name match', async () => {
        const results = await dataManager.findEntitiesByName('Gandalf');
        expect(results).toHaveLength(1);
        expect(results[0].id).toBe('gandalf');
      });

      test('should find entities by alias', async () => {
        const results = await dataManager.findEntitiesByName('Strider');
        expect(results).toHaveLength(1);
        expect(results[0].id).toBe('aragorn');
      });

      test('should be case insensitive', async () => {
        const results = await dataManager.findEntitiesByName('MITHRANDIR');
        expect(results).toHaveLength(1);
        expect(results[0].id).toBe('gandalf');
      });

      test('should support fuzzy matching for transcription errors', async () => {
        // Test with slight misspelling
        const results = await dataManager.findEntitiesByName('Gandlf', true);
        expect(results).toHaveLength(1);
        expect(results[0].id).toBe('gandalf');
      });

      test('should return empty array for no matches', async () => {
        const results = await dataManager.findEntitiesByName('Sauron');
        expect(results).toEqual([]);
      });
    });

    describe('findEntitiesByLocation', () => {
      test('should find all entities at specified location', async () => {
        const results = await dataManager.findEntitiesByLocation('rivendell');
        expect(results).toHaveLength(1);
        expect(results[0].id).toBe('gandalf');
      });
    });

    describe('findEntitiesByTag', () => {
      test('should find all entities with specified tag', async () => {
        const results = await dataManager.findEntitiesByTag('important');
        expect(results).toHaveLength(2);
        expect(results.map(r => r.id).sort()).toEqual(['aragorn', 'gandalf']);
      });
    });
  });

  describe('Real-time Update Operations', () => {
    beforeEach(async () => {
      await dataManager.initializeCampaign({
        name: 'Update Test Campaign',
        transcriptionFile: '/path/to/test.log'
      });

      await dataManager.saveEntity('npcs', 'test-npc', {
        id: 'test-npc',
        name: 'Test NPC',
        mentionCount: 0,
        lastMentioned: null,
        timeline: []
      });
    });

    describe('updateEntityMention', () => {
      test('should update mention count and timestamp', async () => {
        const context = {
          timestamp: '2024-08-19T19:15:00Z',
          context: 'The wizard approaches the party'
        };

        await dataManager.updateEntityMention('npcs', 'test-npc', context);

        const updated = await dataManager.loadEntity('npcs', 'test-npc');
        expect(updated.mentionCount).toBe(1);
        expect(updated.lastMentioned).toBe(context.timestamp);
        expect(updated.timeline).toHaveLength(1);
        expect(updated.timeline[0]).toMatchObject({
          timestamp: context.timestamp,
          event: 'mention',
          context: context.context
        });
      });

      test('should increment mention count on subsequent mentions', async () => {
        await dataManager.updateEntityMention('npcs', 'test-npc', {
          timestamp: '2024-08-19T19:15:00Z',
          context: 'First mention'
        });

        await dataManager.updateEntityMention('npcs', 'test-npc', {
          timestamp: '2024-08-19T19:20:00Z',
          context: 'Second mention'
        });

        const updated = await dataManager.loadEntity('npcs', 'test-npc');
        expect(updated.mentionCount).toBe(2);
        expect(updated.timeline).toHaveLength(2);
      });
    });

    describe('logSessionEvent', () => {
      test('should log session events with timestamp', async () => {
        const event = {
          type: 'location_change',
          entityType: 'location',
          entityId: 'rivendell',
          context: 'Party arrives in Rivendell'
        };

        await dataManager.logSessionEvent(event);

        // Check that session file was created/updated
        const sessionDate = new Date().toISOString().split('T')[0];
        const sessionFile = path.join(campaignPath, 'sessions', `${sessionDate}.json`);

        const sessionData = JSON.parse(await fs.readFile(sessionFile, 'utf8'));
        expect(sessionData.events).toHaveLength(1);
        expect(sessionData.events[0]).toMatchObject(event);
        expect(sessionData.events[0].timestamp).toBeDefined();
      });
    });
  });

  describe('Performance and Caching', () => {
    beforeEach(async () => {
      await dataManager.initializeCampaign({
        name: 'Performance Test Campaign',
        transcriptionFile: '/path/to/test.log'
      });
    });

    test('should cache entities after first load', async () => {
      const entityData = { id: 'perf-test', name: 'Performance Test' };
      await dataManager.saveEntity('npcs', 'perf-test', entityData);

      // Clear cache to force file read on first load
      dataManager.cache.clear();

      // First load - from file
      const start1 = process.hrtime.bigint();
      await dataManager.loadEntity('npcs', 'perf-test');
      const time1 = Number(process.hrtime.bigint() - start1);

      // Second load - from cache (should be faster)
      const start2 = process.hrtime.bigint();
      const cachedEntity = await dataManager.loadEntity('npcs', 'perf-test');
      const time2 = Number(process.hrtime.bigint() - start2);

      // Verify entity was loaded correctly
      expect(cachedEntity.id).toBe('perf-test');

      // Cache should be faster (or at least not slower)
      expect(time2).toBeLessThanOrEqual(time1);
    });

    test('should rebuild indexes efficiently', async () => {
      // Add multiple entities
      for (let i = 0; i < 10; i++) {
        await dataManager.saveEntity('npcs', `npc-${i}`, {
          id: `npc-${i}`,
          name: `NPC ${i}`,
          aliases: [`Alias ${i}`],
          tags: [`tag-${i % 3}`]
        });
      }

      const start = Date.now();
      await dataManager.rebuildIndexes();
      const rebuildTime = Date.now() - start;

      // Should complete quickly
      expect(rebuildTime).toBeLessThan(100);

      // Verify indexes are correct
      expect(dataManager.indexes.byName.size).toBeGreaterThan(10); // Names + aliases
      expect(dataManager.indexes.byTag.get('tag-0')).toHaveLength(4); // NPCs 0, 3, 6, 9
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle corrupted JSON files gracefully', async () => {
      await dataManager.initializeCampaign({
        name: 'Error Test Campaign',
        transcriptionFile: '/path/to/test.log'
      });

      // Create corrupted file
      const corruptedPath = path.join(campaignPath, 'npcs', 'corrupted.json');
      await fs.writeFile(corruptedPath, '{ invalid json }');

      await expect(dataManager.loadEntity('npcs', 'corrupted'))
        .rejects.toThrow('Failed to parse entity data');
    });

    test('should handle missing directories gracefully', async () => {
      // Don't initialize campaign first
      await expect(dataManager.saveEntity('npcs', 'test', { id: 'test' }))
        .rejects.toThrow('Campaign not initialized');
    });

    test('should validate entity data structure', async () => {
      await dataManager.initializeCampaign({
        name: 'Validation Test Campaign',
        transcriptionFile: '/path/to/test.log'
      });

      // Missing required id field
      await expect(dataManager.saveEntity('npcs', 'invalid', { name: 'No ID' }))
        .rejects.toThrow('Entity must have an id field');

      // ID mismatch
      await expect(dataManager.saveEntity('npcs', 'mismatch', { id: 'different' }))
        .rejects.toThrow('Entity id must match the provided id');
    });

    test('should handle concurrent access safely', async () => {
      await dataManager.initializeCampaign({
        name: 'Concurrency Test Campaign',
        transcriptionFile: '/path/to/test.log'
      });

      // Simulate concurrent saves
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          dataManager.saveEntity('npcs', `concurrent-${i}`, {
            id: `concurrent-${i}`,
            name: `Concurrent ${i}`
          })
        );
      }

      await Promise.all(promises);

      // All entities should be saved correctly
      for (let i = 0; i < 5; i++) {
        const entity = await dataManager.loadEntity('npcs', `concurrent-${i}`);
        expect(entity.name).toBe(`Concurrent ${i}`);
      }
    });
  });

  describe('Backup and Recovery', () => {
    beforeEach(async () => {
      await dataManager.initializeCampaign({
        name: 'Backup Test Campaign',
        transcriptionFile: '/path/to/test.log'
      });
    });

    test('should create backup before major operations', async () => {
      // Add some data
      await dataManager.saveEntity('npcs', 'backup-test', {
        id: 'backup-test',
        name: 'Backup Test NPC'
      });

      await dataManager.createBackup();

      // Check backup was created
      const backupDir = path.join(campaignPath, 'backups');
      const backups = await fs.readdir(backupDir);
      expect(backups.length).toBeGreaterThan(0);

      // Verify backup contains data
      const latestBackup = backups.sort().pop();
      const backupPath = path.join(backupDir, latestBackup);
      const backupNpcPath = path.join(backupPath, 'npcs', 'backup-test.json');

      const backupData = JSON.parse(await fs.readFile(backupNpcPath, 'utf8'));
      expect(backupData.name).toBe('Backup Test NPC');
    });

    test('should restore from backup', async () => {
      // Create initial data
      await dataManager.saveEntity('npcs', 'restore-test', {
        id: 'restore-test',
        name: 'Original Name'
      });

      await dataManager.createBackup();

      // Modify data
      await dataManager.saveEntity('npcs', 'restore-test', {
        id: 'restore-test',
        name: 'Modified Name'
      });

      // Get backup list and restore from latest
      const backups = await dataManager.listBackups();
      await dataManager.restoreFromBackup(backups[0]);

      // Verify restoration
      const restored = await dataManager.loadEntity('npcs', 'restore-test');
      expect(restored.name).toBe('Original Name');
    });
  });
});
