/**
 * Test suite for AgentManager
 * Following TDD approach - comprehensive tests before implementation
 */

import { jest } from '@jest/globals';
import { EventEmitter } from 'events';
import { AgentManager } from '../../src/agent-manager.js';
import { BaseAgent } from '../../src/agents/base-agent.js';

// Mock agent for testing
class MockAgent extends BaseAgent {
  constructor(dataManager) {
    super();
    this.dataManager = dataManager;
    this.processedContent = [];
    this.initializeCalled = false;
  }

  async initialize() {
    this.initializeCalled = true;
    this.isInitialized = true;
    this.emit('agentReady', { agentType: 'MockAgent' });
  }

  async process(content, metadata = {}) {
    this.requireInitialization();
    this.processedContent.push({ content, metadata });
    this.emit('contentProcessed', { content, metadata });
  }

  canProcess(content) {
    return content && content.includes('mock');
  }
}

// Another mock agent for testing multiple agents
class SecondMockAgent extends BaseAgent {
  constructor(dataManager) {
    super();
    this.dataManager = dataManager;
    this.processedContent = [];
  }

  async initialize() {
    this.isInitialized = true;
    this.emit('agentReady', { agentType: 'SecondMockAgent' });
  }

  async process(content, metadata = {}) {
    this.requireInitialization();
    this.processedContent.push({ content, metadata });
  }
}

describe('AgentManager', () => {
  let agentManager;
  let mockDataManager;
  let mockBroadcastFunction;

  beforeEach(() => {
    mockDataManager = {
      loadEntity: jest.fn(),
      saveEntity: jest.fn(),
      findEntitiesByName: jest.fn()
    };

    mockBroadcastFunction = jest.fn();
    
    agentManager = new AgentManager(mockDataManager, mockBroadcastFunction);
  });

  afterEach(async () => {
    if (agentManager) {
      await agentManager.stopSystem();
    }
  });

  describe('Constructor and Initialization', () => {
    test('should create instance with correct properties', () => {
      expect(agentManager).toBeInstanceOf(EventEmitter);
      expect(agentManager.dataManager).toBe(mockDataManager);
      expect(agentManager.broadcastToClients).toBe(mockBroadcastFunction);
      expect(agentManager.agents).toBeInstanceOf(Map);
      expect(agentManager.agents.size).toBe(0);
      expect(agentManager.isRunning).toBe(false);
    });

    test('should start system successfully', async () => {
      await agentManager.startSystem();
      
      expect(agentManager.isRunning).toBe(true);
    });

    test('should stop system successfully', async () => {
      await agentManager.startSystem();
      await agentManager.stopSystem();
      
      expect(agentManager.isRunning).toBe(false);
    });
  });

  describe('Agent Registration', () => {
    test('should register agent successfully', async () => {
      const agent = await agentManager.registerAgent(MockAgent);
      
      expect(agent).toBeInstanceOf(MockAgent);
      expect(agent.dataManager).toBe(mockDataManager);
      expect(agent.isInitialized).toBe(true);
      expect(agentManager.agents.has('MockAgent')).toBe(true);
      expect(agentManager.agents.get('MockAgent')).toBe(agent);
    });

    test('should apply configuration during registration', async () => {
      const config = {
        settings: {
          fuzzyThreshold: 0.8,
          cooldownMs: 1000
        }
      };
      
      const agent = await agentManager.registerAgent(MockAgent, config);
      
      expect(agent.settings).toEqual(config.settings);
    });

    test('should set up event forwarding for agent events', async () => {
      const agent = await agentManager.registerAgent(MockAgent);
      
      // Emit an agent event
      agent.emitAgentEvent('testEvent', { test: 'data' });
      
      expect(mockBroadcastFunction).toHaveBeenCalledWith({
        type: 'agentEvent',
        agentType: 'MockAgent',
        eventType: 'testEvent',
        data: { test: 'data' },
        timestamp: expect.any(String)
      });
    });

    test('should handle agent initialization errors', async () => {
      class FailingAgent extends BaseAgent {
        async initialize() {
          throw new Error('Initialization failed');
        }
      }
      
      await expect(agentManager.registerAgent(FailingAgent))
        .rejects.toThrow('Initialization failed');
      
      expect(agentManager.agents.has('FailingAgent')).toBe(false);
    });

    test('should register multiple agents', async () => {
      const agent1 = await agentManager.registerAgent(MockAgent);
      const agent2 = await agentManager.registerAgent(SecondMockAgent);
      
      expect(agentManager.agents.size).toBe(2);
      expect(agentManager.agents.has('MockAgent')).toBe(true);
      expect(agentManager.agents.has('SecondMockAgent')).toBe(true);
    });

    test('should prevent duplicate agent registration', async () => {
      await agentManager.registerAgent(MockAgent);
      
      await expect(agentManager.registerAgent(MockAgent))
        .rejects.toThrow('Agent MockAgent is already registered');
    });
  });

  describe('Content Processing', () => {
    beforeEach(async () => {
      await agentManager.startSystem();
    });

    test('should process content through registered agents', async () => {
      const agent = await agentManager.registerAgent(MockAgent);
      
      const content = 'This is mock content for testing';
      const metadata = { timestamp: '2024-08-19T19:15:00Z' };
      
      await agentManager.processContent(content, metadata);
      
      expect(agent.processedContent).toHaveLength(1);
      expect(agent.processedContent[0]).toEqual({ content, metadata });
    });

    test('should process content through multiple agents in parallel', async () => {
      const agent1 = await agentManager.registerAgent(MockAgent);
      const agent2 = await agentManager.registerAgent(SecondMockAgent);
      
      const content = 'This is mock content for testing';
      
      await agentManager.processContent(content);
      
      // MockAgent should process (content includes 'mock')
      expect(agent1.processedContent).toHaveLength(1);
      
      // SecondMockAgent should also process (no canProcess filter)
      expect(agent2.processedContent).toHaveLength(1);
    });

    test('should skip agents that cannot process content', async () => {
      const agent = await agentManager.registerAgent(MockAgent);
      
      const content = 'This content has no special keywords';
      
      await agentManager.processContent(content);
      
      // MockAgent should not process (content doesn't include 'mock')
      expect(agent.processedContent).toHaveLength(0);
    });

    test('should handle empty or invalid content gracefully', async () => {
      await agentManager.registerAgent(MockAgent);
      
      await agentManager.processContent('');
      await agentManager.processContent(null);
      await agentManager.processContent(undefined);
      
      // Should not throw errors
      expect(true).toBe(true);
    });

    test('should not process content when system is stopped', async () => {
      const agent = await agentManager.registerAgent(MockAgent);
      await agentManager.stopSystem();
      
      await agentManager.processContent('This is mock content');
      
      expect(agent.processedContent).toHaveLength(0);
    });

    test('should isolate agent errors during processing', async () => {
      class FailingAgent extends BaseAgent {
        async initialize() {
          this.isInitialized = true;
        }
        
        async process() {
          throw new Error('Processing failed');
        }
      }
      
      const workingAgent = await agentManager.registerAgent(MockAgent);
      const failingAgent = await agentManager.registerAgent(FailingAgent);
      
      // Should not throw error despite failing agent
      await agentManager.processContent('This is mock content');
      
      // Working agent should still process successfully
      expect(workingAgent.processedContent).toHaveLength(1);
      
      // Error should be broadcast
      expect(mockBroadcastFunction).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'agentError',
          agentType: 'FailingAgent',
          error: 'FailingAgent: Processing failed'
        })
      );
    });
  });

  describe('System Lifecycle', () => {
    test('should start system and emit systemStarted event', async () => {
      const systemListener = jest.fn();
      agentManager.on('systemStarted', systemListener);
      
      await agentManager.startSystem();
      
      expect(agentManager.isRunning).toBe(true);
      expect(systemListener).toHaveBeenCalled();
    });

    test('should stop system and cleanup all agents', async () => {
      const agent1 = await agentManager.registerAgent(MockAgent);
      const agent2 = await agentManager.registerAgent(SecondMockAgent);
      
      const cleanup1 = jest.spyOn(agent1, 'cleanup');
      const cleanup2 = jest.spyOn(agent2, 'cleanup');
      
      await agentManager.startSystem();
      await agentManager.stopSystem();
      
      expect(agentManager.isRunning).toBe(false);
      expect(cleanup1).toHaveBeenCalled();
      expect(cleanup2).toHaveBeenCalled();
    });

    test('should handle agent cleanup errors gracefully', async () => {
      class FailingCleanupAgent extends BaseAgent {
        async initialize() {
          this.isInitialized = true;
        }
        
        cleanup() {
          throw new Error('Cleanup failed');
        }
      }
      
      await agentManager.registerAgent(FailingCleanupAgent);
      await agentManager.startSystem();
      
      // Should not throw error despite failing cleanup
      await expect(agentManager.stopSystem()).resolves.not.toThrow();
      
      expect(agentManager.isRunning).toBe(false);
    });

    test('should emit systemStopped event when stopping', async () => {
      const systemListener = jest.fn();
      agentManager.on('systemStopped', systemListener);
      
      await agentManager.startSystem();
      await agentManager.stopSystem();
      
      expect(systemListener).toHaveBeenCalled();
    });
  });

  describe('Agent Management', () => {
    test('should unregister agent successfully', async () => {
      const agent = await agentManager.registerAgent(MockAgent);
      const cleanupSpy = jest.spyOn(agent, 'cleanup');
      
      await agentManager.unregisterAgent('MockAgent');
      
      expect(agentManager.agents.has('MockAgent')).toBe(false);
      expect(cleanupSpy).toHaveBeenCalled();
    });

    test('should handle unregistering non-existent agent', async () => {
      await expect(agentManager.unregisterAgent('NonExistentAgent'))
        .rejects.toThrow('Agent NonExistentAgent not found');
    });

    test('should get agent status information', async () => {
      const agent = await agentManager.registerAgent(MockAgent);
      
      const status = agentManager.getAgentStatus('MockAgent');
      
      expect(status).toEqual({
        agentType: 'MockAgent',
        isInitialized: true,
        settings: {},
        listenerCount: expect.any(Number)
      });
    });

    test('should get system status information', async () => {
      await agentManager.registerAgent(MockAgent);
      await agentManager.registerAgent(SecondMockAgent);
      await agentManager.startSystem();
      
      const status = agentManager.getSystemStatus();
      
      expect(status).toEqual({
        isRunning: true,
        agentCount: 2,
        agents: ['MockAgent', 'SecondMockAgent']
      });
    });
  });
});
