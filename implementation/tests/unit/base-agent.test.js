/**
 * Test suite for BaseAgent abstract class
 * Following TDD approach - comprehensive tests before implementation
 */

import { jest } from '@jest/globals';
import { EventEmitter } from 'events';
import { BaseAgent } from '../../src/agents/base-agent.js';

describe('BaseAgent', () => {
  let agent;

  beforeEach(() => {
    agent = new BaseAgent();
  });

  afterEach(() => {
    if (agent) {
      agent.cleanup();
    }
  });

  describe('Constructor and Initialization', () => {
    test('should create instance with correct properties', () => {
      expect(agent).toBeInstanceOf(EventEmitter);
      expect(agent.isInitialized).toBe(false);
      expect(agent.settings).toEqual({});
    });

    test('should throw error when initialize() is not implemented', async () => {
      await expect(agent.initialize()).rejects.toThrow('initialize() must be implemented');
    });

    test('should throw error when process() is not implemented', async () => {
      await expect(agent.process('test content')).rejects.toThrow('process() must be implemented');
    });
  });

  describe('Optional Methods', () => {
    test('canProcess() should return true by default', () => {
      expect(agent.canProcess('any content')).toBe(true);
    });

    test('cleanup() should remove all listeners', () => {
      const mockListener = jest.fn();
      agent.on('test', mockListener);
      
      expect(agent.listenerCount('test')).toBe(1);
      
      agent.cleanup();
      
      expect(agent.listenerCount('test')).toBe(0);
    });
  });

  describe('Error Handling Utilities', () => {
    test('safeDataOperation() should execute operation successfully', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      const result = await agent.safeDataOperation(mockOperation);
      
      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    test('safeDataOperation() should return fallback on error', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Test error'));
      const fallback = 'fallback value';
      
      const result = await agent.safeDataOperation(mockOperation, fallback);
      
      expect(result).toBe(fallback);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    test('safeDataOperation() should emit agentError event on failure', async () => {
      // Create a named function for proper operation name detection
      function mockOperation() {
        throw new Error('Test error');
      }
      const errorListener = jest.fn();

      agent.on('agentError', errorListener);

      await agent.safeDataOperation(mockOperation, null);

      expect(errorListener).toHaveBeenCalledWith({
        agentType: 'BaseAgent',
        operation: 'mockOperation',
        error: 'Test error',
        timestamp: expect.any(String)
      });
    });

    test('safeDataOperation() should return null fallback by default', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Test error'));
      
      const result = await agent.safeDataOperation(mockOperation);
      
      expect(result).toBeNull();
    });
  });

  describe('Settings Management', () => {
    test('should allow setting configuration', () => {
      const settings = { 
        fuzzyThreshold: 0.8,
        cooldownMs: 1000 
      };
      
      Object.assign(agent.settings, settings);
      
      expect(agent.settings).toEqual(settings);
    });

    test('should preserve existing settings when adding new ones', () => {
      agent.settings.existing = 'value';
      
      Object.assign(agent.settings, { new: 'setting' });
      
      expect(agent.settings).toEqual({
        existing: 'value',
        new: 'setting'
      });
    });
  });

  describe('Event Emission', () => {
    test('should emit events correctly', () => {
      const listener = jest.fn();
      agent.on('testEvent', listener);
      
      const eventData = { test: 'data' };
      agent.emit('testEvent', eventData);
      
      expect(listener).toHaveBeenCalledWith(eventData);
    });

    test('should support multiple listeners', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();
      
      agent.on('testEvent', listener1);
      agent.on('testEvent', listener2);
      
      agent.emit('testEvent', 'data');
      
      expect(listener1).toHaveBeenCalledWith('data');
      expect(listener2).toHaveBeenCalledWith('data');
    });
  });
});

// Test implementation of BaseAgent for testing concrete functionality
class TestAgent extends BaseAgent {
  constructor(dataManager) {
    super();
    this.dataManager = dataManager;
    this.processedContent = [];
  }

  async initialize() {
    this.isInitialized = true;
    this.emit('agentReady', { agentType: 'TestAgent' });
  }

  async process(content) {
    if (!this.isInitialized) {
      throw new Error('Agent not initialized');
    }
    
    this.processedContent.push(content);
    this.emit('contentProcessed', { content });
  }

  canProcess(content) {
    return !!(content && typeof content === 'string' && content.trim().length > 0);
  }
}

describe('BaseAgent Implementation (TestAgent)', () => {
  let testAgent;
  let mockDataManager;

  beforeEach(() => {
    mockDataManager = {
      loadEntity: jest.fn(),
      saveEntity: jest.fn(),
      findEntitiesByName: jest.fn()
    };
    
    testAgent = new TestAgent(mockDataManager);
  });

  afterEach(() => {
    if (testAgent) {
      testAgent.cleanup();
    }
  });

  describe('Concrete Implementation', () => {
    test('should initialize successfully', async () => {
      const readyListener = jest.fn();
      testAgent.on('agentReady', readyListener);
      
      await testAgent.initialize();
      
      expect(testAgent.isInitialized).toBe(true);
      expect(readyListener).toHaveBeenCalledWith({ agentType: 'TestAgent' });
    });

    test('should process content when initialized', async () => {
      await testAgent.initialize();
      
      const processListener = jest.fn();
      testAgent.on('contentProcessed', processListener);
      
      await testAgent.process('test content');
      
      expect(testAgent.processedContent).toContain('test content');
      expect(processListener).toHaveBeenCalledWith({ content: 'test content' });
    });

    test('should throw error when processing before initialization', async () => {
      await expect(testAgent.process('test content'))
        .rejects.toThrow('Agent not initialized');
    });

    test('canProcess should filter empty content', () => {
      expect(testAgent.canProcess('valid content')).toBe(true);
      expect(testAgent.canProcess('')).toBe(false);
      expect(testAgent.canProcess('   ')).toBe(false);
      expect(testAgent.canProcess(null)).toBe(false);
      expect(testAgent.canProcess(undefined)).toBe(false);
    });

    test('should have access to injected dataManager', () => {
      expect(testAgent.dataManager).toBe(mockDataManager);
    });
  });

  describe('Error Handling in Concrete Implementation', () => {
    test('should handle dataManager errors gracefully', async () => {
      await testAgent.initialize();
      
      mockDataManager.loadEntity.mockRejectedValue(new Error('Database error'));
      
      const result = await testAgent.safeDataOperation(
        () => mockDataManager.loadEntity('npcs', 'test-id'),
        'fallback'
      );
      
      expect(result).toBe('fallback');
    });

    test('should emit agentError for dataManager failures', async () => {
      await testAgent.initialize();
      
      const errorListener = jest.fn();
      testAgent.on('agentError', errorListener);
      
      mockDataManager.saveEntity.mockRejectedValue(new Error('Save failed'));
      
      await testAgent.safeDataOperation(
        () => mockDataManager.saveEntity('npcs', 'test', {}),
        null
      );
      
      expect(errorListener).toHaveBeenCalledWith(
        expect.objectContaining({
          agentType: 'TestAgent',
          error: 'Save failed'
        })
      );
    });
  });
});
